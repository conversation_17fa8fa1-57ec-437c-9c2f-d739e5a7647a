-- Enable HTTP extension for making API calls from database
CREATE EXTENSION IF NOT EXISTS http;

-- Function to add new user to SendGrid contact list via backend API
CREATE OR REPLACE FUNCTION basejump.add_user_to_sendgrid()
    RETURNS TRIGGER
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = public
AS $$
DECLARE
    backend_url text := 'https://backend.atlasagents.ai/api/send-welcome-email-background';
    user_name text;
    response_result http_response;
BEGIN
    -- Extract user name from metadata or email
    IF NEW.raw_user_meta_data->>'full_name' IS NOT NULL THEN
        user_name := NEW.raw_user_meta_data->>'full_name';
    ELSIF NEW.raw_user_meta_data->>'name' IS NOT NULL THEN
        user_name := NEW.raw_user_meta_data->>'name';
    ELSIF NEW.email IS NOT NULL THEN
        user_name := split_part(NEW.email, '@', 1);
    END IF;

    -- Make HTTP POST request to backend API
    SELECT http(
        ('POST', 
         backend_url,
         ARRAY[
             http_header('Content-Type', 'application/json')
         ],
         'application/json',
         json_build_object(
             'email', NEW.email,
             'name', user_name
         )::text
        )
    ) INTO response_result;

    -- Log the response for debugging (optional)
    RAISE LOG 'SendGrid API call response: status=%, content=%', 
        response_result.status, 
        response_result.content;

    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the user creation process
        RAISE LOG 'Error calling SendGrid API: %', SQLERRM;
        RETURN NEW;
END;
$$;

-- Create trigger to call SendGrid integration after user creation
CREATE TRIGGER on_auth_user_created_sendgrid
    AFTER INSERT
    ON auth.users
    FOR EACH ROW
EXECUTE FUNCTION basejump.add_user_to_sendgrid();