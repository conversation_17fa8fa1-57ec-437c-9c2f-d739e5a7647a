"""
Public API endpoints for agent templates

This module provides public access to marketplace templates
for unauthenticated users (e.g., during sign-up flow)
"""

from fastapi import APIRouter, HTTPException
from typing import List, Optional
from utils.logger import logger
from mcp_local.template_manager import template_manager
from mcp_local.secure_api import TemplateResponse

router = APIRouter()

@router.get("/public/templates/starter", response_model=List[TemplateResponse])
async def get_starter_templates(limit: int = 4):
    """Get starter templates for sign-up flow - no authentication required"""
    logger.info(f"Getting starter templates (limit: {limit})")
    
    try:
        # Get templates from marketplace, preferring Kortix team templates
        templates = await template_manager.get_marketplace_templates(
            limit=limit,
            offset=0,
            search=None,
            tags=None
        )
        
        logger.info(f"Found {len(templates)} starter templates")
        return [TemplateResponse(**template) for template in templates]
        
    except Exception as e:
        logger.error(f"Error fetching starter templates: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch starter templates")

@router.get("/public/templates", response_model=List[TemplateResponse])
async def get_public_templates(
    limit: int = 20,
    offset: int = 0,
    search: Optional[str] = None,
    tags: Optional[str] = None
):
    """Get all public templates - no authentication required"""
    logger.info(f"Getting public templates (limit: {limit}, offset: {offset}, search: {search}, tags: {tags})")
    
    try:
        # Parse tags if provided
        tag_list = None
        if tags:
            tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
        
        templates = await template_manager.get_marketplace_templates(
            limit=limit,
            offset=offset,
            search=search,
            tags=tag_list
        )
        
        return [TemplateResponse(**template) for template in templates]
        
    except Exception as e:
        logger.error(f"Error fetching public templates: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch public templates")