# Chat Input Configuration Panel Documentation

## Overview

The Chat Input Configuration Panel (often referred to as the "pocket") is a collapsible configuration interface that appears below the chat input when an agent is selected and advanced configuration is enabled. It provides quick access to agent configuration options including integrations, instructions, knowledge base, triggers, and workflows.

## Component Location

**Main Component:** `frontend/src/components/thread/chat-input/chat-input.tsx`

The panel is conditionally rendered when:

- `enableAdvancedConfig` is true
- `selectedAgentId` is not null/undefined

## Visual Structure

```
┌─────────────────────────────────────────────────────────┐
│                    Chat Input                           │
├─────────────────────────────────────────────────────────┤
│ [🔗 Integrations] │ [🧠 Instructions] │ [📊 Knowledge] │
│ [⚡ Triggers] │ [🔄 Workflows]                        │
└─────────────────────────────────────────────────────────┘
```

## Key Features

1. **Integrations Button**: Opens PipedreamRegistry for connecting 2700+ apps
2. **Instructions Button**: Opens AgentConfigModal to edit agent instructions
3. **Knowledge Button**: Opens knowledge base management
4. **Triggers Button**: Opens trigger configuration
5. **Workflows Button**: Opens workflow configuration

## Component Dependencies

### Core Files

1. **Main Chat Input Component**

   - `frontend/src/components/thread/chat-input/chat-input.tsx`

2. **Modal Components**

   - `frontend/src/components/agents/agent-config-modal.tsx`
   - `frontend/src/components/agents/pipedream/pipedream-registry.tsx`

3. **Configuration Sub-components**

   - `frontend/src/components/agents/agent-tools-configuration.tsx`
   - `frontend/src/components/agents/agent-mcp-configuration.tsx`
   - `frontend/src/components/agents/knowledge-base/agent-knowledge-base-manager.tsx`
   - `frontend/src/components/agents/triggers/agent-triggers-configuration.tsx`
   - `frontend/src/components/agents/workflows/agent-workflows-configuration.tsx`

4. **Supporting Components**
   - `frontend/src/components/thread/chat-input/message-input.tsx`
   - `frontend/src/components/thread/chat-input/agent-selector.tsx`
   - `frontend/src/components/agents/pipedream/pipedream-connector.tsx`
   - `frontend/src/components/agents/mcp/tools-manager.tsx`

### UI Components Used

- `@/components/ui/dialog`
- `@/components/ui/button`
- `@/components/ui/tabs`
- `@/components/ui/input`
- `@/components/ui/textarea`
- `@/components/ui/badge`
- `@/components/ui/card`

### Icons Used

- `lucide-react`: Brain, Database, Zap, Workflow, Settings2, Bot
- `react-icons/fa`: FaGoogle, FaDiscord
- `react-icons/si`: SiNotion

### Hooks and Utilities

- `@/hooks/react-query/agents/use-agents`
- `@/hooks/react-query/pipedream/use-pipedream`
- `@/hooks/use-modal-store`
- `@/lib/utils`

## State Management

The panel manages several state variables:

- `configModalOpen`: Controls AgentConfigModal visibility
- `configModalTab`: Determines which tab to open in the modal
- `registryDialogOpen`: Controls PipedreamRegistry visibility
- `selectedAgentId`: Currently selected agent
- `enableAdvancedConfig`: Whether advanced features are enabled

## Styling

The panel uses Tailwind CSS classes:

- `bg-muted/20`: Light background
- `rounded-b-3xl`: Rounded bottom corners
- `border-t border-border/30`: Top border
- `px-4 py-1.5`: Padding
- Hover effects with `hover:bg-muted/50` and `hover:text-foreground`

## Integration Points

1. **Agent Selection**: Integrates with AgentSelector component
2. **Modal System**: Uses Dialog components for overlays
3. **Data Persistence**: Updates agent configuration via API calls
4. **Real-time Updates**: Uses React Query for data synchronization

## File Structure

```
frontend/src/components/
├── thread/chat-input/
│   ├── chat-input.tsx                 # Main component with config panel
│   ├── message-input.tsx              # Contains agent selector
│   └── agent-selector.tsx             # Agent selection dropdown
├── agents/
│   ├── agent-config-modal.tsx         # Main configuration modal
│   ├── agent-tools-configuration.tsx  # Tools configuration
│   ├── agent-mcp-configuration.tsx    # MCP configuration
│   ├── knowledge-base/
│   │   └── agent-knowledge-base-manager.tsx
│   ├── triggers/
│   │   └── agent-triggers-configuration.tsx
│   ├── workflows/
│   │   └── agent-workflows-configuration.tsx
│   └── pipedream/
│       ├── pipedream-registry.tsx     # Integrations registry
│       └── pipedream-connector.tsx    # Connection handler
└── ui/                                # Reusable UI components
```

---

## Complete File Contents

Below are the complete contents of all files required for the Chat Input Configuration Panel:

### 1. Main Chat Input Component

#### File: frontend/src/components/thread/chat-input/chat-input.tsx

````typescript
'use client';

import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';

import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { handleFiles } from './file-upload-handler';
import { MessageInput } from './message-input';
import { AttachmentGroup } from '../attachment-group';
import { useModelSelection } from './_use-model-selection';
import { useFileDelete } from '@/hooks/react-query/files';
import { useQueryClient } from '@tanstack/react-query';
import { ToolCallInput } from './floating-tool-preview';
import { ChatSnack } from './chat-snack';
import { Brain, ChevronRight, Zap, Workflow, Database, Wrench, X } from 'lucide-react';
import { FaGoogle, FaDiscord } from 'react-icons/fa';
import { SiNotion } from 'react-icons/si';
import { AgentConfigModal } from '@/components/agents/agent-config-modal';
import { PipedreamRegistry } from '@/components/agents/pipedream/pipedream-registry';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useSubscriptionWithStreaming } from '@/hooks/react-query/subscriptions/use-subscriptions';
import { isLocalMode } from '@/lib/config';
import { Button } from '@/components/ui/button';
import { AnimatePresence } from 'framer-motion';
import { BillingModal } from '@/components/billing/billing-modal';

export interface ChatInputHandles {
  getPendingFiles: () => File[];
  clearPendingFiles: () => void;
}

export interface ChatInputProps {
  onSubmit: (
    message: string,
    options?: { model_name?: string; enable_thinking?: boolean },
  ) => void;
  placeholder?: string;
  loading?: boolean;
  disabled?: boolean;
  isAgentRunning?: boolean;
  onStopAgent?: () => void;
  autoFocus?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  onFileBrowse?: () => void;
  sandboxId?: string;
  hideAttachments?: boolean;
  selectedAgentId?: string;
  onAgentSelect?: (agentId: string | undefined) => void;
  agentName?: string;
  messages?: any[];
  bgColor?: string;
  toolCalls?: ToolCallInput[];
  toolCallIndex?: number;
  showToolPreview?: boolean;
  onExpandToolPreview?: () => void;
  isLoggedIn?: boolean;
  enableAdvancedConfig?: boolean;
  onConfigureAgent?: (agentId: string) => void;
  hideAgentSelection?: boolean;
  defaultShowSnackbar?: 'tokens' | 'upgrade' | false;
  showToLowCreditUsers?: boolean;
}

export interface UploadedFile {
  name: string;
  path: string;
  size: number;
  type: string;
  localUrl?: string;
}



export const ChatInput = forwardRef<ChatInputHandles, ChatInputProps>(
  (
    {
      onSubmit,
      placeholder = 'Describe what you need help with...',
      loading = false,
      disabled = false,
      isAgentRunning = false,
      onStopAgent,
      autoFocus = true,
      value: controlledValue,
      onChange: controlledOnChange,
      onFileBrowse,
      sandboxId,
      hideAttachments = false,
      selectedAgentId,
      onAgentSelect,
      agentName,
      messages = [],
      bgColor = 'bg-card',
      toolCalls = [],
      toolCallIndex = 0,
      showToolPreview = false,
      onExpandToolPreview,
      isLoggedIn = true,
      enableAdvancedConfig = false,
      onConfigureAgent,
      hideAgentSelection = false,
      defaultShowSnackbar = false,
      showToLowCreditUsers = true,
    },
    ref,
  ) => {
    const isControlled =
      controlledValue !== undefined && controlledOnChange !== undefined;

    const [uncontrolledValue, setUncontrolledValue] = useState('');
    const value = isControlled ? controlledValue : uncontrolledValue;

    const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
    const [pendingFiles, setPendingFiles] = useState<File[]>([]);
    const [isUploading, setIsUploading] = useState(false);
    const [isDraggingOver, setIsDraggingOver] = useState(false);
    const [configModalOpen, setConfigModalOpen] = useState(false);
    const [configModalTab, setConfigModalTab] = useState('integrations');
    const [registryDialogOpen, setRegistryDialogOpen] = useState(false);
    const [showSnackbar, setShowSnackbar] = useState(defaultShowSnackbar);
    const [userDismissedUsage, setUserDismissedUsage] = useState(false);
    const [billingModalOpen, setBillingModalOpen] = useState(false);

    const {
      selectedModel,
      setSelectedModel: handleModelChange,
      subscriptionStatus,
      allModels: modelOptions,
      canAccessModel,
      getActualModelId,
      refreshCustomModels,
    } = useModelSelection();

    const { data: subscriptionData } = useSubscriptionWithStreaming(isAgentRunning);
    const deleteFileMutation = useFileDelete();
    const queryClient = useQueryClient();

    // Show usage preview logic:
    // - Always show to free users when showToLowCreditUsers is true
    // - For paid users, only show when they're at 70% or more of their cost limit (30% or below remaining)
    const shouldShowUsage = !isLocalMode() && subscriptionData && showToLowCreditUsers && (() => {
      // Free users: always show
      if (subscriptionStatus === 'no_subscription') {
        return true;
      }

      // Paid users: only show when at 70% or more of cost limit
      const currentUsage = subscriptionData.current_usage || 0;
      const costLimit = subscriptionData.cost_limit || 0;

      if (costLimit === 0) return false; // No limit set

      return currentUsage >= (costLimit * 0.7); // 70% or more used (30% or less remaining)
    })();

    // Auto-show usage preview when we have subscription data
    useEffect(() => {
      if (shouldShowUsage && defaultShowSnackbar !== false && !userDismissedUsage && (showSnackbar === false || showSnackbar === defaultShowSnackbar)) {
        setShowSnackbar('upgrade');
      } else if (!shouldShowUsage && showSnackbar !== false) {
        setShowSnackbar(false);
      }
    }, [subscriptionData, showSnackbar, defaultShowSnackbar, shouldShowUsage, subscriptionStatus, showToLowCreditUsers, userDismissedUsage]);

    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const hasLoadedFromLocalStorage = useRef(false);

    useImperativeHandle(ref, () => ({
      getPendingFiles: () => pendingFiles,
      clearPendingFiles: () => setPendingFiles([]),
    }));

    useEffect(() => {
      if (typeof window !== 'undefined' && onAgentSelect && !hasLoadedFromLocalStorage.current) {
        const urlParams = new URLSearchParams(window.location.search);
        const hasAgentIdInUrl = urlParams.has('agent_id');

        if (!selectedAgentId && !hasAgentIdInUrl) {
          const savedAgentId = localStorage.getItem('lastSelectedAgentId');
          if (savedAgentId) {
            const agentIdToSelect = savedAgentId === 'suna' ? undefined : savedAgentId;
            console.log('Loading saved agent from localStorage:', savedAgentId);
            onAgentSelect(agentIdToSelect);
          } else {
            console.log('No saved agent found in localStorage');
          }
        } else {
          console.log('Skipping localStorage load:', {
            hasSelectedAgent: !!selectedAgentId,
            hasAgentIdInUrl,
            selectedAgentId
          });
        }
        hasLoadedFromLocalStorage.current = true;
      }
    }, [onAgentSelect, selectedAgentId]); // Keep selectedAgentId to check current state

    // Save selected agent to localStorage whenever it changes
    useEffect(() => {
      if (typeof window !== 'undefined') {
        // Use 'suna' as a special key for the default agent (undefined)
        const keyToStore = selectedAgentId === undefined ? 'suna' : selectedAgentId;
        console.log('Saving selected agent to localStorage:', keyToStore);
        localStorage.setItem('lastSelectedAgentId', keyToStore);
      }
    }, [selectedAgentId]);

    useEffect(() => {
      if (autoFocus && textareaRef.current) {
        textareaRef.current.focus();
      }
    }, [autoFocus]);

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      if (
        (!value.trim() && uploadedFiles.length === 0) ||
        loading ||
        (disabled && !isAgentRunning)
      )
        return;

      if (isAgentRunning && onStopAgent) {
        onStopAgent();
        return;
      }

      let message = value;

      if (uploadedFiles.length > 0) {
        const fileInfo = uploadedFiles
          .map((file) => `[Uploaded File: ${file.path}]`)
          .join('\n');
        message = message ? `${message}\n\n${fileInfo}` : fileInfo;
      }

      let baseModelName = getActualModelId(selectedModel);
      let thinkingEnabled = false;
      if (selectedModel.endsWith('-thinking')) {
        baseModelName = getActualModelId(selectedModel.replace(/-thinking$/, ''));
        thinkingEnabled = true;
      }

      onSubmit(message, {
        model_name: baseModelName,
        enable_thinking: thinkingEnabled,
      });

      if (!isControlled) {
        setUncontrolledValue('');
      }

      setUploadedFiles([]);
    };

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      if (isControlled) {
        controlledOnChange(newValue);
      } else {
        setUncontrolledValue(newValue);
      }
    };

    const handleTranscription = (transcribedText: string) => {
      const currentValue = isControlled ? controlledValue : uncontrolledValue;
      const newValue = currentValue ? `${currentValue} ${transcribedText}` : transcribedText;

      if (isControlled) {
        controlledOnChange(newValue);
      } else {
        setUncontrolledValue(newValue);
      }
    };

    const removeUploadedFile = (index: number) => {
      const fileToRemove = uploadedFiles[index];

      // Clean up local URL if it exists
      if (fileToRemove.localUrl) {
        URL.revokeObjectURL(fileToRemove.localUrl);
      }

      // Remove from local state immediately for responsive UI
      setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
      if (!sandboxId && pendingFiles.length > index) {
        setPendingFiles((prev) => prev.filter((_, i) => i !== index));
      }

      // Check if file is referenced in existing chat messages before deleting from server
      const isFileUsedInChat = messages.some(message => {
        const content = typeof message.content === 'string' ? message.content : '';
        return content.includes(`[Uploaded File: ${fileToRemove.path}]`);
      });

      // Only delete from server if file is not referenced in chat history
      if (sandboxId && fileToRemove.path && !isFileUsedInChat) {
        deleteFileMutation.mutate({
          sandboxId,
          filePath: fileToRemove.path,
        }, {
          onError: (error) => {
            console.error('Failed to delete file from server:', error);
          }
        });
      } else if (isFileUsedInChat) {
        console.log(`Skipping server deletion for ${fileToRemove.path} - file is referenced in chat history`);
      }
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDraggingOver(true);
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDraggingOver(false);
    };



    return (
      <div className="mx-auto w-full max-w-4xl relative">
        <div className="relative">
          <ChatSnack
            toolCalls={toolCalls}
            toolCallIndex={toolCallIndex}
            onExpandToolPreview={onExpandToolPreview}
            agentName={agentName}
            showToolPreview={showToolPreview}
            showUsagePreview={showSnackbar}
            subscriptionData={subscriptionData}
            onCloseUsage={() => { setShowSnackbar(false); setUserDismissedUsage(true); }}
            onOpenUpgrade={() => setBillingModalOpen(true)}
            isVisible={showToolPreview || !!showSnackbar}
          />
          <Card
            className={`-mb-2 shadow-none w-full max-w-4xl mx-auto bg-transparent border-none overflow-visible ${enableAdvancedConfig && selectedAgentId ? '' : 'rounded-3xl'} relative`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsDraggingOver(false);
              if (fileInputRef.current && e.dataTransfer.files.length > 0) {
                const files = Array.from(e.dataTransfer.files);
                handleFiles(
                  files,
                  sandboxId,
                  setPendingFiles,
                  setUploadedFiles,
                  setIsUploading,
                  messages,
                  queryClient,
                );
              }
            }}
          >


            <div className="w-full text-sm flex flex-col justify-between items-start rounded-lg">
              <CardContent className={`w-full p-1.5 ${enableAdvancedConfig && selectedAgentId ? 'pb-1' : 'pb-2'} ${bgColor} border ${enableAdvancedConfig && selectedAgentId ? 'rounded-t-3xl' : 'rounded-3xl'}`}>
                <AttachmentGroup
                  files={uploadedFiles || []}
                  sandboxId={sandboxId}
                  onRemove={removeUploadedFile}
                  layout="inline"
                  maxHeight="216px"
                  showPreviews={true}
                />
                <MessageInput
                  ref={textareaRef}
                  value={value}
                  onChange={handleChange}
                  onSubmit={handleSubmit}
                  onTranscription={handleTranscription}
                  placeholder={placeholder}
                  loading={loading}
                  disabled={disabled}
                  isAgentRunning={isAgentRunning}
                  onStopAgent={onStopAgent}
                  isDraggingOver={isDraggingOver}
                  uploadedFiles={uploadedFiles}

                  fileInputRef={fileInputRef}
                  isUploading={isUploading}
                  sandboxId={sandboxId}
                  setPendingFiles={setPendingFiles}
                  setUploadedFiles={setUploadedFiles}
                  setIsUploading={setIsUploading}
                  hideAttachments={hideAttachments}
                  messages={messages}

                  selectedModel={selectedModel}
                  onModelChange={handleModelChange}
                  modelOptions={modelOptions}
                  subscriptionStatus={subscriptionStatus}
                  canAccessModel={canAccessModel}
                  refreshCustomModels={refreshCustomModels}
                  isLoggedIn={isLoggedIn}

                  selectedAgentId={selectedAgentId}
                  onAgentSelect={onAgentSelect}
                  hideAgentSelection={hideAgentSelection}
                />
              </CardContent>

              {enableAdvancedConfig && selectedAgentId && (
                <div className="w-full border-t border-border/30 bg-muted/20 px-4 py-1.5 rounded-b-3xl border-l border-r border-b border-border">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto scrollbar-none">
                      <button
                        onClick={() => setRegistryDialogOpen(true)}
                        className="flex items-center gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2.5 py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0"
                      >
                        <div className="flex items-center -space-x-0.5">
                          <div className="w-5 h-5 bg-white dark:bg-muted border border-border rounded-full flex items-center justify-center shadow-sm">
                            <FaGoogle className="w-3 h-3" />
                          </div>
                          <div className="w-5 h-5 bg-white dark:bg-muted border border-border rounded-full flex items-center justify-center shadow-sm">
                            <FaDiscord className="w-3 h-3" />
                          </div>
                          <div className="w-5 h-5 bg-white dark:bg-muted border border-border rounded-full flex items-center justify-center shadow-sm">
                            <SiNotion className="w-3 h-3" />
                          </div>
                        </div>
                        <span className="text-xs font-medium">Integrations</span>
                      </button>

                      <div className="w-px h-4 bg-border/60" />

                      <button
                        onClick={() => {
                          setConfigModalTab('instructions');
                          setConfigModalOpen(true);
                        }}
                        className="flex items-center gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2.5 py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0"
                      >
                        <Brain className="h-3.5 w-3.5 flex-shrink-0" />
                        <span className="text-xs font-medium">Instructions</span>
                      </button>

                      <div className="w-px h-4 bg-border/60" />

                      <button
                        onClick={() => {
                          setConfigModalTab('knowledge');
                          setConfigModalOpen(true);
                        }}
                        className="flex items-center gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2.5 py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0"
                      >
                        <Database className="h-3.5 w-3.5 flex-shrink-0" />
                        <span className="text-xs font-medium">Knowledge</span>
                      </button>

                      <div className="w-px h-4 bg-border/60" />

                      <button
                        onClick={() => {
                          setConfigModalTab('triggers');
                          setConfigModalOpen(true);
                        }}
                        className="flex items-center gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2.5 py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0"
                      >
                        <Zap className="h-3.5 w-3.5 flex-shrink-0" />
                        <span className="text-xs font-medium">Triggers</span>
                      </button>

                      <div className="w-px h-4 bg-border/60" />

                      <button
                        onClick={() => {
                          setConfigModalTab('workflows');
                          setConfigModalOpen(true);
                        }}
                        className="flex items-center gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2.5 py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0"
                      >
                        <Workflow className="h-3.5 w-3.5 flex-shrink-0" />
                        <span className="text-xs font-medium">Workflows</span>
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </Card>
          <AgentConfigModal
            isOpen={configModalOpen}
            onOpenChange={setConfigModalOpen}
            selectedAgentId={selectedAgentId}
            onAgentSelect={onAgentSelect}
            initialTab={configModalTab}
          />
          <Dialog open={registryDialogOpen} onOpenChange={setRegistryDialogOpen}>
            <DialogContent className="p-0 max-w-6xl max-h-[90vh] overflow-y-auto">
              <DialogHeader className="sr-only">
                <DialogTitle>Integrations</DialogTitle>
              </DialogHeader>
              <PipedreamRegistry
                showAgentSelector={true}
                selectedAgentId={selectedAgentId}
                onAgentChange={onAgentSelect}
                onToolsSelected={(profileId, selectedTools, appName, appSlug) => {
                  console.log('Tools selected:', { profileId, selectedTools, appName, appSlug });
                }}
              />
            </DialogContent>
          </Dialog>
          <BillingModal
            open={billingModalOpen}
            onOpenChange={setBillingModalOpen}
          />
        </div>
      </div>
    );
  },
);

ChatInput.displayName = 'ChatInput';```

### 2. Agent Config Modal

#### File: frontend/src/components/agents/agent-config-modal.tsx

```typescript
'use client';

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Settings2, Brain, Database, Zap, Workflow, Bot } from 'lucide-react';
import { AgentMCPConfiguration } from './agent-mcp-configuration';
import { AgentTriggersConfiguration } from './triggers/agent-triggers-configuration';
import { AgentWorkflowsConfiguration } from './workflows/agent-workflows-configuration';
import { AgentKnowledgeBaseManager } from './knowledge-base/agent-knowledge-base-manager';
import { AgentToolsConfiguration } from './agent-tools-configuration';
import { AgentSelector } from '../thread/chat-input/agent-selector';
import { useAgent, useUpdateAgent } from '@/hooks/react-query/agents/use-agents';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

interface AgentConfigModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedAgentId?: string;
  onAgentSelect?: (agentId: string | undefined) => void;
  initialTab?: string;
}

export const AgentConfigModal: React.FC<AgentConfigModalProps> = ({
  isOpen,
  onOpenChange,
  selectedAgentId,
  onAgentSelect,
  initialTab = 'tools'
}) => {
  const [activeTab, setActiveTab] = useState(initialTab);
  const [editingInstructions, setEditingInstructions] = useState(false);
  const [instructionsValue, setInstructionsValue] = useState('');
  const [agentName, setAgentName] = useState('');
  const [agentDescription, setAgentDescription] = useState('');

  const { data: agent, isLoading } = useAgent(selectedAgentId || '');
  const updateAgentMutation = useUpdateAgent();
  const router = useRouter();

  // Update active tab when initialTab changes or modal opens
  React.useEffect(() => {
    if (isOpen && initialTab) {
      setActiveTab(initialTab);
    }
  }, [initialTab, isOpen]);

  // Update local state when agent data changes
  React.useEffect(() => {
    if (agent) {
      setAgentName(agent.name || '');
      setAgentDescription(agent.description || '');
      setInstructionsValue(agent.system_prompt || '');
    }
  }, [agent]);

  const handleSaveInstructions = async () => {
    if (!selectedAgentId) return;

    try {
      await updateAgentMutation.mutateAsync({
        agentId: selectedAgentId,
        name: agentName,
        description: agentDescription,
        system_prompt: instructionsValue
      });
      toast.success('Agent updated successfully');
      setEditingInstructions(false);
    } catch (error) {
      toast.error('Failed to update agent');
    }
  };

  const handleToolsChange = async (tools: Record<string, { enabled: boolean; description: string }>) => {
    if (!selectedAgentId) return;

    try {
      await updateAgentMutation.mutateAsync({
        agentId: selectedAgentId,
        agentpress_tools: tools
      });
      toast.success('Tools updated successfully');
    } catch (error) {
      toast.error('Failed to update tools');
    }
  };

  const handleMCPChange = async (mcps: any) => {
    if (!selectedAgentId) return;

    try {
      await updateAgentMutation.mutateAsync({
        agentId: selectedAgentId,
        configured_mcps: mcps.configured_mcps || [],
        custom_mcps: mcps.custom_mcps || []
      });
      toast.success('Integrations updated successfully');
    } catch (error) {
      toast.error('Failed to update integrations');
    }
  };

  const displayName = agent?.name || 'Suna';

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[85vh] p-0 flex flex-col">
        <DialogHeader className="flex-shrink-0 border-b px-6 py-4">
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bot className="h-4 w-4" />
              Agent Configuration
            </div>
            {selectedAgentId && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/agents/config/${selectedAgentId}`)}
                className="text-xs"
              >
                <Settings2 className="h-3 w-3 mr-1" />
                Advanced
              </Button>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-shrink-0 px-6 py-3 border-b">
          <AgentSelector
            selectedAgentId={selectedAgentId}
            onAgentSelect={onAgentSelect}
          />
        </div>

        <div className="flex-1 min-h-0 px-6 py-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-5 flex-shrink-0 h-9 mb-4">
              <TabsTrigger value="tools" className="text-xs">
                <Settings2 className="h-3 w-3 mr-1" />
                Tools
              </TabsTrigger>
              <TabsTrigger value="instructions" className="text-xs">
                <Brain className="h-3 w-3 mr-1" />
                Instructions
              </TabsTrigger>
              <TabsTrigger value="knowledge" className="text-xs">
                <Database className="h-3 w-3 mr-1" />
                Knowledge
              </TabsTrigger>
              <TabsTrigger value="triggers" className="text-xs">
                <Zap className="h-3 w-3 mr-1" />
                Triggers
              </TabsTrigger>
              <TabsTrigger value="workflows" className="text-xs">
                <Workflow className="h-3 w-3 mr-1" />
                Workflows
              </TabsTrigger>
            </TabsList>

            <TabsContent value="tools" className="flex-1 m-0 mt-0 overflow-y-auto overflow-hidden">
              <div className="h-full">
                {selectedAgentId ? (
                  <AgentToolsConfiguration
                    tools={agent?.agentpress_tools || {}}
                    onToolsChange={handleToolsChange}
                  />
                ) : (
                  <div className="flex items-center justify-center h-32">
                    <p className="text-sm text-muted-foreground">Select an agent to configure tools</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="instructions" className="flex-1 m-0 mt-0 overflow-y-auto overflow-hidden">
              <div className="h-full flex flex-col">
                {selectedAgentId ? (
                  <>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="agent-name" className="text-sm">Name</Label>
                        <Input
                          id="agent-name"
                          value={agentName}
                          onChange={(e) => setAgentName(e.target.value)}
                          placeholder="Agent name"
                          className="h-8"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="agent-description" className="text-sm">Description</Label>
                        <Input
                          id="agent-description"
                          value={agentDescription}
                          onChange={(e) => setAgentDescription(e.target.value)}
                          placeholder="Brief description"
                          className="h-8"
                        />
                      </div>
                    </div>

                    <div className="space-y-2 flex-1 flex flex-col">
                      <Label htmlFor="system-instructions" className="text-sm">System Instructions</Label>
                      <Textarea
                        id="system-instructions"
                        value={instructionsValue}
                        onChange={(e) => setInstructionsValue(e.target.value)}
                        placeholder="Define the agent's role, behavior, and expertise..."
                        className="flex-1 resize-none"
                      />
                    </div>

                    <div className="flex gap-2 pt-4">
                      <Button
                        onClick={handleSaveInstructions}
                        disabled={updateAgentMutation.isPending}
                        size="sm"
                      >
                        {updateAgentMutation.isPending ? 'Saving...' : 'Save'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setAgentName(agent?.name || '');
                          setAgentDescription(agent?.description || '');
                          setInstructionsValue(agent?.system_prompt || '');
                        }}
                      >
                        Reset
                      </Button>
                    </div>
                  </>
                ) : (
                  <div className="flex items-center justify-center h-32">
                    <p className="text-sm text-muted-foreground">Select an agent to configure instructions</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="knowledge" className="flex-1 m-0 mt-0 overflow-y-auto overflow-hidden">
              <div className="h-full">
                {selectedAgentId ? (
                  <AgentKnowledgeBaseManager
                    agentId={selectedAgentId}
                    agentName={agentName}
                  />
                ) : (
                  <div className="flex items-center justify-center h-32">
                    <p className="text-sm text-muted-foreground">Select an agent to manage knowledge base</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="triggers" className="flex-1 m-0 mt-0 overflow-y-auto overflow-hidden">
              <div className="h-full">
                {selectedAgentId ? (
                  <AgentTriggersConfiguration agentId={selectedAgentId} />
                ) : (
                  <div className="flex items-center justify-center h-32">
                    <p className="text-sm text-muted-foreground">Select an agent to configure triggers</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="workflows" className="flex-1 m-0 mt-0 overflow-y-auto overflow-hidden">
              <div className="h-full">
                {selectedAgentId ? (
                  <AgentWorkflowsConfiguration
                    agentId={selectedAgentId}
                    agentName={agentName}
                  />
                ) : (
                  <div className="flex items-center justify-center h-32">
                    <p className="text-sm text-muted-foreground">Select an agent to configure workflows</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}; ```

### 3. Message Input Component

#### File: frontend/src/components/thread/chat-input/message-input.tsx

```typescript
import React, { forwardRef, useEffect, useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Square, Loader2, ArrowUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { UploadedFile } from './chat-input';
import { FileUploadHandler } from './file-upload-handler';
import { VoiceRecorder } from './voice-recorder';
import { ModelSelector } from './model-selector';
import { AgentSelector } from './agent-selector';
import { canAccessModel, SubscriptionStatus } from './_use-model-selection';
import { isLocalMode } from '@/lib/config';
import { useFeatureFlag } from '@/lib/feature-flags';
import { TooltipContent } from '@/components/ui/tooltip';
import { Tooltip } from '@/components/ui/tooltip';
import { TooltipProvider, TooltipTrigger } from '@radix-ui/react-tooltip';
import { BillingModal } from '@/components/billing/billing-modal';
import ChatDropdown from './chat-dropdown';
import { handleFiles } from './file-upload-handler';

interface MessageInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onSubmit: (e: React.FormEvent) => void;
  onTranscription: (text: string) => void;
  placeholder: string;
  loading: boolean;
  disabled: boolean;
  isAgentRunning: boolean;
  onStopAgent?: () => void;
  isDraggingOver: boolean;
  uploadedFiles: UploadedFile[];

  fileInputRef: React.RefObject<HTMLInputElement>;
  isUploading: boolean;
  sandboxId?: string;
  setPendingFiles: React.Dispatch<React.SetStateAction<File[]>>;
  setUploadedFiles: React.Dispatch<React.SetStateAction<UploadedFile[]>>;
  setIsUploading: React.Dispatch<React.SetStateAction<boolean>>;
  hideAttachments?: boolean;
  messages?: any[]; // Add messages prop
  isLoggedIn?: boolean;

  selectedModel: string;
  onModelChange: (model: string) => void;
  modelOptions: any[];
  subscriptionStatus: SubscriptionStatus;
  canAccessModel: (modelId: string) => boolean;
  refreshCustomModels?: () => void;
  selectedAgentId?: string;
  onAgentSelect?: (agentId: string | undefined) => void;
  enableAdvancedConfig?: boolean;
  hideAgentSelection?: boolean;
}

export const MessageInput = forwardRef<HTMLTextAreaElement, MessageInputProps>(
  (
    {
      value,
      onChange,
      onSubmit,
      onTranscription,
      placeholder,
      loading,
      disabled,
      isAgentRunning,
      onStopAgent,
      isDraggingOver,
      uploadedFiles,

      fileInputRef,
      isUploading,
      sandboxId,
      setPendingFiles,
      setUploadedFiles,
      setIsUploading,
      hideAttachments = false,
      messages = [],
      isLoggedIn = true,

      selectedModel,
      onModelChange,
      modelOptions,
      subscriptionStatus,
      canAccessModel,
      refreshCustomModels,

      selectedAgentId,
      onAgentSelect,
      enableAdvancedConfig = false,
      hideAgentSelection = false,
    },
    ref,
  ) => {
    const [billingModalOpen, setBillingModalOpen] = useState(false);
    const { enabled: customAgentsEnabled, loading: flagsLoading } = useFeatureFlag('custom_agents');

    useEffect(() => {
      const textarea = ref as React.RefObject<HTMLTextAreaElement>;
      if (!textarea.current) return;

      const adjustHeight = () => {
        textarea.current!.style.height = 'auto';
        const newHeight = Math.min(
          Math.max(textarea.current!.scrollHeight, 24),
          200,
        );
        textarea.current!.style.height = `${newHeight}px`;
      };

      adjustHeight();

      // Call it twice to ensure proper height calculation
      adjustHeight();

      window.addEventListener('resize', adjustHeight);
      return () => window.removeEventListener('resize', adjustHeight);
    }, [value, ref]);

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key === 'Enter' && !e.shiftKey && !e.nativeEvent.isComposing) {
        e.preventDefault();
        if (
          (value.trim() || uploadedFiles.length > 0) &&
          !loading &&
          (!disabled || isAgentRunning)
        ) {
          onSubmit(e as unknown as React.FormEvent);
        }
      }
    };

    const handlePaste = (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
      if (!e.clipboardData) return;
      const items = Array.from(e.clipboardData.items);
      const imageFiles: File[] = [];
      for (const item of items) {
        if (item.kind === 'file' && item.type.startsWith('image/')) {
          const file = item.getAsFile();
          if (file) imageFiles.push(file);
        }
      }
      if (imageFiles.length > 0) {
        e.preventDefault();
        handleFiles(
          imageFiles,
          sandboxId,
          setPendingFiles,
          setUploadedFiles,
          setIsUploading,
          messages,
        );
      }
    };

    const renderDropdown = () => {
      if (isLoggedIn) {
        const showAdvancedFeatures = enableAdvancedConfig || (customAgentsEnabled && !flagsLoading);

        return (
          <div className="flex items-center gap-2">
            {showAdvancedFeatures && !hideAgentSelection && (
              <AgentSelector
                selectedAgentId={selectedAgentId}
                onAgentSelect={onAgentSelect}
                disabled={loading || (disabled && !isAgentRunning)}
              />
            )}
            <ModelSelector
              selectedModel={selectedModel}
              onModelChange={onModelChange}
              modelOptions={modelOptions}
              subscriptionStatus={subscriptionStatus}
              canAccessModel={canAccessModel}
              refreshCustomModels={refreshCustomModels}
              billingModalOpen={billingModalOpen}
              setBillingModalOpen={setBillingModalOpen}
            />
          </div>
        );
      }
      return <ChatDropdown />;
    }

    return (
      <div className="relative flex flex-col w-full h-full gap-2 justify-between">

        <div className="flex flex-col gap-1 px-2">
          <Textarea
            ref={ref}
            value={value}
            onChange={onChange}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            placeholder={placeholder}
            className={cn(
              'w-full bg-transparent dark:bg-transparent border-none shadow-none focus-visible:ring-0 px-0.5 pb-6 pt-4 !text-[15px] min-h-[36px] max-h-[200px] overflow-y-auto resize-none',
              isDraggingOver ? 'opacity-40' : '',
            )}
            disabled={loading || (disabled && !isAgentRunning)}
            rows={1}
          />
        </div>


        <div className="flex items-center justify-between mt-0 mb-1 px-2">
          <div className="flex items-center gap-3">
            {!hideAttachments && (
              <FileUploadHandler
                ref={fileInputRef}
                loading={loading}
                disabled={disabled}
                isAgentRunning={isAgentRunning}
                isUploading={isUploading}
                sandboxId={sandboxId}
                setPendingFiles={setPendingFiles}
                setUploadedFiles={setUploadedFiles}
                setIsUploading={setIsUploading}
                messages={messages}
                isLoggedIn={isLoggedIn}
              />
            )}

          </div>

          {/* {subscriptionStatus === 'no_subscription' && !isLocalMode() &&
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <p role='button' className='text-sm text-amber-500 hidden sm:block cursor-pointer' onClick={() => setBillingModalOpen(true)}>Upgrade for more usage</p>
                </TooltipTrigger>
                <TooltipContent>
                  <p>The free tier is severely limited by the amount of usage. Upgrade to experience the full power of Suna.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          } */}

          <div className='flex items-center gap-2'>
            {renderDropdown()}
            <BillingModal
              open={billingModalOpen}
              onOpenChange={setBillingModalOpen}
              returnUrl={typeof window !== 'undefined' ? window.location.href : '/'}
            />

            {isLoggedIn && <VoiceRecorder
              onTranscription={onTranscription}
              disabled={loading || (disabled && !isAgentRunning)}
            />}

            <Button
              type="submit"
              onClick={isAgentRunning && onStopAgent ? onStopAgent : onSubmit}
              size="sm"
              className={cn(
                'w-8 h-8 flex-shrink-0 self-end rounded-xl',
                (!value.trim() && uploadedFiles.length === 0 && !isAgentRunning) ||
                  loading ||
                  (disabled && !isAgentRunning)
                  ? 'opacity-50'
                  : '',
              )}
              disabled={
                (!value.trim() && uploadedFiles.length === 0 && !isAgentRunning) ||
                loading ||
                (disabled && !isAgentRunning)
              }
            >
              {loading ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : isAgentRunning ? (
                <div className="min-h-[14px] min-w-[14px] w-[14px] h-[14px] rounded-sm bg-current" />
              ) : (
                <ArrowUp className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>
        {/* {subscriptionStatus === 'no_subscription' && !isLocalMode() &&
          <div className='sm:hidden absolute -bottom-8 left-0 right-0 flex justify-center'>
            <p className='text-xs text-amber-500 px-2 py-1'>
              Upgrade for better performance
            </p>
          </div>
        } */}
      </div>
    );
  },
);

MessageInput.displayName = 'MessageInput';```

### 4. Agent Selector Component

#### File: frontend/src/components/thread/chat-input/agent-selector.tsx

```typescript
'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Settings, ChevronRight, Bot, Presentation, FileSpreadsheet, Search, Plus, User, Check, ChevronDown } from 'lucide-react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useAgents, useCreateNewAgent } from '@/hooks/react-query/agents/use-agents';

import { useRouter } from 'next/navigation';
import { cn, truncateString } from '@/lib/utils';

interface PredefinedAgent {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: 'productivity' | 'creative' | 'development';
}

const PREDEFINED_AGENTS: PredefinedAgent[] = [
  // {
  //   id: 'slides',
  //   name: 'Slides',
  //   description: 'Create stunning presentations and slide decks',
  //   icon: <Presentation className="h-4 w-4" />,
  //   category: 'productivity'
  // },
  // {
  //   id: 'sheets',
  //   name: 'Sheets',
  //   description: 'Spreadsheet and data analysis expert',
  //   icon: <FileSpreadsheet className="h-4 w-4" />,
  //   category: 'productivity'
  // }
];

interface AgentSelectorProps {
  selectedAgentId?: string;
  onAgentSelect?: (agentId: string | undefined) => void;
  disabled?: boolean;
}

export const AgentSelector: React.FC<AgentSelectorProps> = ({
  selectedAgentId,
  onAgentSelect,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);
  const [isCreatingAgent, setIsCreatingAgent] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const { data: agentsResponse, isLoading: agentsLoading } = useAgents();
  const agents = agentsResponse?.agents || [];
  const createNewAgentMutation = useCreateNewAgent();

  // Combine all agents
  const allAgents = [
    {
      id: undefined,
      name: 'Suna',
      description: 'Your personal AI assistant',
      type: 'default' as const,
      icon: <Image src="/kortix-symbol.svg" alt="Suna" width={16} height={16} className="h-4 w-4 dark:invert" />
    },
    ...PREDEFINED_AGENTS.map(agent => ({
      ...agent,
      type: 'predefined' as const
    })),
    ...agents.map((agent: any) => ({
      ...agent,
      id: agent.agent_id,
      type: 'custom' as const,
      icon: agent.avatar || <Bot className="h-4 w-4" />
    }))
  ];

  // Filter agents based on search query
  const filteredAgents = allAgents.filter((agent) =>
    agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    agent.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 50);
    } else {
      setSearchQuery('');
      setHighlightedIndex(-1);
    }
  }, [isOpen]);

  const getAgentDisplay = () => {
    const selectedAgent = allAgents.find(agent => agent.id === selectedAgentId);

    if (selectedAgent) {
      console.log('Selected agent found:', selectedAgent.name, 'with ID:', selectedAgent.id);
      return {
        name: selectedAgent.name,
        icon: selectedAgent.icon
      };
    }

    // If selectedAgentId is not undefined but no agent is found, log a warning
    if (selectedAgentId !== undefined) {
      console.warn('Agent with ID', selectedAgentId, 'not found, falling back to Suna');
    }

    // Default to Suna (the first agent which has id: undefined)
    const defaultAgent = allAgents[0];
    console.log('Using default agent:', defaultAgent.name);
    return {
      name: defaultAgent.name,
      icon: defaultAgent.icon
    };
  };

  const handleAgentSelect = (agentId: string | undefined) => {
    console.log('Agent selected:', agentId === undefined ? 'Suna (default)' : agentId);
    onAgentSelect?.(agentId);
    setIsOpen(false);
  };

  const handleAgentSettings = (agentId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen(false);
    router.push(`/agents/config/${agentId}`);
  };

  const handleSearchInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    e.stopPropagation();
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setHighlightedIndex((prev) =>
        prev < filteredAgents.length - 1 ? prev + 1 : 0
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setHighlightedIndex((prev) =>
        prev > 0 ? prev - 1 : filteredAgents.length - 1
      );
    } else if (e.key === 'Enter' && highlightedIndex >= 0) {
      e.preventDefault();
      const selectedAgent = filteredAgents[highlightedIndex];
      if (selectedAgent) {
        handleAgentSelect(selectedAgent.id);
      }
    }
  };

  const handleExploreAll = () => {
    setIsOpen(false);
    router.push('/agents');
  };

  const handleCreateAgent = useCallback(() => {
    if (isCreatingAgent || createNewAgentMutation.isPending) {
      return; // Prevent multiple clicks
    }

    setIsCreatingAgent(true);
    setIsOpen(false);

    createNewAgentMutation.mutate(undefined, {
      onSettled: () => {
        // Reset the debounce state after mutation completes (success or error)
        setTimeout(() => setIsCreatingAgent(false), 1000);
      }
    });
  }, [isCreatingAgent, createNewAgentMutation]);

  const renderAgentItem = (agent: any, index: number) => {
    const isSelected = agent.id === selectedAgentId;
    const isHighlighted = index === highlightedIndex;
    const hasSettings = agent.type === 'custom' && agent.id;

    return (
      <TooltipProvider key={agent.id || 'default'}>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuItem
              className={cn(
                "flex items-center rounded-xl gap-3 px-4 py-2.5 cursor-pointer hover:bg-accent/40 transition-colors duration-200 group",
                isHighlighted && "bg-accent/40"
              )}
              onClick={() => handleAgentSelect(agent.id)}
              onMouseEnter={() => setHighlightedIndex(index)}
            >
              <div className="flex-shrink-0">
                {agent.icon}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm text-foreground/90 truncate">
                    {agent.name}
                  </span>
                </div>
                <span className="text-xs text-muted-foreground/80 truncate leading-relaxed">
                  {truncateString(agent.description, 30)}
                </span>
              </div>
              <div className="flex items-center gap-2 flex-shrink-0">
                {hasSettings && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-muted/60 rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-200"
                    onClick={(e) => handleAgentSettings(agent.id, e)}
                  >
                    <Settings className="h-3 w-3" />
                  </Button>
                )}
                {isSelected && (
                  <div className="h-6 w-6 rounded-full bg-blue-500/10 flex items-center justify-center">
                    <Check className="h-3 w-3 text-blue-600/80" />
                  </div>
                )}
              </div>
            </DropdownMenuItem>
          </TooltipTrigger>
          <TooltipContent side="left" className="text-xs max-w-xs">
            <p className="truncate">{truncateString(agent.description, 35)}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  const agentDisplay = getAgentDisplay();

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "px-2.5 py-1.5 text-sm font-normal hover:bg-accent/40 transition-all duration-200 rounded-xl",
                    "focus:ring-1 focus:ring-ring focus:ring-offset-1 focus:outline-none",
                    isOpen && "bg-accent/40"
                  )}
                  disabled={disabled}
                >
                  <div className="flex items-center gap-2">
                    <div className="flex-shrink-0">
                      {agentDisplay.icon}
                    </div>
                    <span className="hidden sm:inline-block truncate max-w-[80px] font-normal">
                      {agentDisplay.name}
                    </span>
                    <ChevronDown
                      size={12}
                      className={cn(
                        "opacity-50 transition-transform duration-200",
                        isOpen && "rotate-180"
                      )}
                    />
                  </div>
                </Button>
              </DropdownMenuTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>Select Agent</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <DropdownMenuContent
          align="end"
          className="w-88 p-0 border-0 shadow-md bg-card/98 backdrop-blur-sm"
          sideOffset={6}
          style={{
            borderRadius: '20px'
          }}
        >
          <div className="p-4 pb-3">
            <div className="relative">
              <Search className="absolute left-3 top-2.5 h-3.5 w-3.5 text-muted-foreground/60" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search agents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={handleSearchInputKeyDown}
                className={cn(
                  "w-full pl-10 pr-3 py-2 text-sm bg-muted/40 border-0 rounded-xl",
                  "focus:outline-none focus:ring-1 focus:ring-ring focus:ring-offset-0 focus:bg-muted/60",
                  "placeholder:text-muted-foreground/60 transition-all duration-200"
                )}
              />
            </div>
          </div>

          {/* Agent List */}
          <div className="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent px-1.5">
            {agentsLoading ? (
              <div className="px-4 py-6 text-sm text-muted-foreground/70 text-center">
                <div className="animate-pulse">Loading agents...</div>
              </div>
            ) : filteredAgents.length === 0 ? (
              <div className="px-4 py-6 text-sm text-muted-foreground/70 text-center">
                <Search className="h-6 w-6 mx-auto mb-2 opacity-40" />
                <p>No agents found</p>
                <p className="text-xs mt-1 opacity-60">Try adjusting your search</p>
              </div>
            ) : (
              <div className="space-y-0.5">
                {filteredAgents.map((agent, index) => renderAgentItem(agent, index))}
              </div>
            )}
          </div>

          {/* Footer Actions */}
          <div className="p-4 pt-3 border-t border-border/40">
            <div className="flex items-center justify-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleExploreAll}
                className="text-xs flex items-center gap-2 rounded-xl hover:bg-accent/40 transition-all duration-200 text-muted-foreground hover:text-foreground px-4 py-2"
              >
                <Search className="h-3.5 w-3.5" />
                Explore All Agents
              </Button>

              <div className="w-px h-4 bg-border/60" />

              <Button
                variant="ghost"
                size="sm"
                onClick={handleCreateAgent}
                disabled={isCreatingAgent || createNewAgentMutation.isPending}
                className="text-xs flex items-center gap-2 rounded-xl hover:bg-accent/40 transition-all duration-200 text-muted-foreground hover:text-foreground px-4 py-2 disabled:opacity-50"
              >
                <Plus className="h-3.5 w-3.5" />
                {isCreatingAgent || createNewAgentMutation.isPending ? 'Creating...' : 'Create Agent'}
              </Button>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

    </>
  );
}; ```

### 5. Pipedream Registry Component

#### File: frontend/src/components/agents/pipedream/pipedream-registry.tsx

```typescript
import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { X, Bot, Search, Sparkles, TrendingUp, Star, Filter, ArrowRight } from 'lucide-react';
import { usePipedreamApps, usePipedreamPopularApps } from '@/hooks/react-query/pipedream/use-pipedream';
import { usePipedreamProfiles } from '@/hooks/react-query/pipedream/use-pipedream-profiles';
import { useAgent } from '@/hooks/react-query/agents/use-agents';
import { PipedreamConnector } from './pipedream-connector';
import { ToolsManager } from '../mcp/tools-manager';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { useQueryClient, useQuery } from '@tanstack/react-query';
import { AgentSelector } from '../../thread/chat-input/agent-selector';
import type { PipedreamApp } from '@/hooks/react-query/pipedream/utils';
import { pipedreamApi } from '@/hooks/react-query/pipedream/utils';
import { AppCard } from './_components/app-card';
import {
  createConnectedAppsFromProfiles,
  getAgentPipedreamProfiles,
} from './utils';
import type { PipedreamRegistryProps, ConnectedApp } from './types';
import { usePathname } from 'next/navigation';

const AppCardSkeleton = () => (
  <div className="group relative overflow-hidden rounded-2xl border border-border bg-card transition-all">
    <div className="p-6">
      <div className="flex items-center gap-4 mb-4">
        <Skeleton className="h-12 w-12 rounded-xl" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-4 w-24" />
        </div>
      </div>
      <div className="space-y-2 mb-4">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
      <div className="flex items-center justify-between">
        <Skeleton className="h-9 w-full rounded-xl" />
      </div>
    </div>
  </div>
);

const AppsGridSkeleton = ({ count = 8 }: { count?: number }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
    {Array.from({ length: count }).map((_, index) => (
      <AppCardSkeleton key={index} />
    ))}
  </div>
);

export const PipedreamRegistry: React.FC<PipedreamRegistryProps> = ({
  onToolsSelected,
  onAppSelected,
  mode = 'full',
  onClose,
  showAgentSelector = false,
  selectedAgentId,
  onAgentChange,
  versionData,
  versionId
}) => {
  const [search, setSearch] = useState('');
  const [showAllApps, setShowAllApps] = useState(false);
  const [showStreamlinedConnector, setShowStreamlinedConnector] = useState(false);
  const [selectedAppForConnection, setSelectedAppForConnection] = useState<PipedreamApp | null>(null);
  const [showToolsManager, setShowToolsManager] = useState(false);
  const [selectedToolsProfile, setSelectedToolsProfile] = useState<{
    profileId: string;
    appName: string;
    profileName: string;
  } | null>(null);
  const pathname = usePathname();
  const isHomePage = pathname.includes('dashboard');

  const [internalSelectedAgentId, setInternalSelectedAgentId] = useState<string | undefined>(selectedAgentId);

  const queryClient = useQueryClient();

  const { data: popularAppsData, isLoading: isLoadingPopular } = usePipedreamPopularApps();

  const shouldFetchAllApps = showAllApps || search.trim() !== '';
  const { data: allAppsData, isLoading: isLoadingAll, error, refetch } = useQuery({
    queryKey: ['pipedream', 'apps', undefined, search],
    queryFn: async () => {
      const result = await pipedreamApi.getApps(undefined, search);
      return result;
    },
    enabled: shouldFetchAllApps,
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });

  const { data: profiles } = usePipedreamProfiles();

  const currentAgentId = selectedAgentId ?? internalSelectedAgentId;
  const { data: agent } = useAgent(currentAgentId || '');

  React.useEffect(() => {
    setInternalSelectedAgentId(selectedAgentId);
  }, [selectedAgentId]);

  const handleAgentSelect = (agentId: string | undefined) => {
    if (onAgentChange) {
      onAgentChange(agentId);
    } else {
      setInternalSelectedAgentId(agentId);
    }
    if (agentId !== currentAgentId) {
      queryClient.invalidateQueries({ queryKey: ['agent'] });
      if (agentId) {
        queryClient.invalidateQueries({ queryKey: ['agent', agentId] });
      }
    }
  };

  const effectiveVersionData = useMemo(() => {
    if (versionData) return versionData;
    if (!agent) return undefined;

    if (agent.current_version) {
      return {
        configured_mcps: agent.current_version.configured_mcps || [],
        custom_mcps: agent.current_version.custom_mcps || [],
        system_prompt: agent.current_version.system_prompt || '',
        agentpress_tools: agent.current_version.agentpress_tools || {}
      };
    }

    return {
      configured_mcps: agent.configured_mcps || [],
      custom_mcps: agent.custom_mcps || [],
      system_prompt: agent.system_prompt || '',
      agentpress_tools: agent.agentpress_tools || {}
    };
  }, [versionData, agent]);

  const agentPipedreamProfiles = useMemo(() => {
    return getAgentPipedreamProfiles(agent, profiles, currentAgentId, effectiveVersionData);
  }, [agent, profiles, currentAgentId, effectiveVersionData]);

  const connectedProfiles = useMemo(() => {
    return profiles?.filter(p => p.is_connected) || [];
  }, [profiles]);

  const connectedApps: ConnectedApp[] = useMemo(() => {
    return createConnectedAppsFromProfiles(connectedProfiles, popularAppsData?.apps || []);
  }, [connectedProfiles, popularAppsData?.apps]);

  const handleSearch = (value: string) => {
    setSearch(value);
    if (value.trim() === '') {
      setShowAllApps(false);
    }
  };

  const handleConnectionComplete = (profileId: string, selectedTools: string[], appName: string, appSlug: string) => {
    if (onToolsSelected) {
      onToolsSelected(profileId, selectedTools, appName, appSlug);
      toast.success(`Added ${selectedTools.length} tools from ${appName}!`);
    }
  };

  const handleConnectApp = (app: PipedreamApp) => {
    setSelectedAppForConnection(app);
    setShowStreamlinedConnector(true);
    onClose?.();
  };

  const handleConfigureTools = (profile: any) => {
    if (!currentAgentId) {
      toast.error('Please select an agent first');
      return;
    }
    setSelectedToolsProfile({
      profileId: profile.profile_id,
      appName: profile.app_name,
      profileName: profile.profile_name
    });
    setShowToolsManager(true);
  };

  const handleClearSearch = () => {
    setSearch('');
    setShowAllApps(false);
  };

  // Determine which apps to show
  const displayApps = useMemo(() => {
    if (search.trim()) {
      return allAppsData?.apps || [];
    }
    if (showAllApps) {
      return allAppsData?.apps || [];
    }
    return popularAppsData?.apps || [];
  }, [search, showAllApps, popularAppsData?.apps, allAppsData?.apps]);

  const isLoading = search.trim() || showAllApps ? isLoadingAll : isLoadingPopular;

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <X className="h-12 w-12 mx-auto mb-2" />
            <p className="text-lg font-semibold">Failed to load integrations</p>
          </div>
          <Button onClick={() => refetch()} className="bg-primary hover:bg-primary/90">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="sticky flex items-center justify-between top-0 z-10 flex-shrink-0 border-b bg-background px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-xl bg-muted-foreground/20 flex items-center justify-center">
              <Sparkles className="h-5 w-5" />
            </div>
            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-xl font-semibold text-foreground">
                  {agent?.name ? `${agent.name} Integrations` : 'Integrations'}
                </h1>
                <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200 dark:border-blue-900 dark:bg-blue-900/20 dark:text-blue-400">
                  2700+ Apps
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                {agent?.name ? 'Connect apps to enhance your agent\'s capabilities' : 'Connect your favorite apps and services'}
              </p>
            </div>
          </div>
          {showAgentSelector && (
            <AgentSelector
              selectedAgentId={currentAgentId}
              onAgentSelect={handleAgentSelect}
            />
          )}
        </div>
        <div className="relative max-w-xl">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search apps..."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10 h-11 w-full bg-muted/50 border-0 focus:bg-background focus:ring-2 focus:ring-primary/20 rounded-xl transition-all"
          />
          {search && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearSearch}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-muted"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          <div className="max-w-7xl mx-auto space-y-8">
            {showAgentSelector && !currentAgentId && (
              <div className="text-center py-12 px-6 bg-gradient-to-br from-muted/30 to-muted/10 rounded-2xl border border-dashed border-border">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl flex items-center justify-center mb-4">
                  <Bot className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">
                  Select an Agent
                </h3>
                <p className="text-sm text-muted-foreground mb-6 max-w-md mx-auto">
                  Choose an agent from the dropdown above to view and manage its integrations
                </p>
              </div>
            )}
            {connectedApps.length > 0 && (!showAgentSelector || currentAgentId) && (
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="border border-green-200 dark:border-green-900 h-8 w-8 rounded-lg bg-green-100 dark:bg-green-900/20 flex items-center justify-center">
                    <Star className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h2 className="text-md font-semibold text-foreground">
                      {showAgentSelector && currentAgentId ? 'Connected to Agent' : 'Connected Apps'}
                    </h2>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {connectedApps.map((app) => (
                    <AppCard
                      key={`${app.name_slug}-${currentAgentId || 'default'}`}
                      app={app}
                      mode={mode}
                      currentAgentId={currentAgentId}
                      agentName={agent?.name}
                      agentPipedreamProfiles={agentPipedreamProfiles}
                      onAppSelected={onAppSelected}
                      onConnectApp={handleConnectApp}
                      onConfigureTools={handleConfigureTools}
                      handleCategorySelect={() => {}}
                    />
                  ))}
                </div>
              </div>
            )}
            {(!showAgentSelector || currentAgentId) && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="h-8 w-8 border border-orange-200 dark:border-orange-900 rounded-lg bg-orange-100 dark:bg-orange-900/20 flex items-center justify-center">
                      <TrendingUp className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                    </div>
                    <div>
                      <h2 className="text-md font-semibold text-foreground">
                        {search.trim() ? 'Search Results' : showAllApps ? 'All Apps' : 'Popular Apps'}
                      </h2>
                    </div>
                  </div>
                </div>
                {isLoading ? (
                  <AppsGridSkeleton count={8} />
                ) : displayApps.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {displayApps.map((app) => (
                      <AppCard
                        key={`${app.name_slug}-${currentAgentId || 'default'}`}
                        app={app}
                        mode={mode}
                        currentAgentId={currentAgentId}
                        agentName={agent?.name}
                        agentPipedreamProfiles={agentPipedreamProfiles}
                        onAppSelected={onAppSelected}
                        onConnectApp={handleConnectApp}
                        onConfigureTools={handleConfigureTools}
                        handleCategorySelect={() => {}}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center flex flex-col items-center justify-center py-12">
                    <div className="text-6xl mb-4">🔍</div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">
                      No apps found
                    </h3>
                    <p className="text-sm text-muted-foreground mb-6 max-w-md mx-auto">
                      {search.trim()
                        ? `No apps match "${search}". Try a different search term.`
                        : 'No apps available at the moment.'
                      }
                    </p>
                    {search.trim() && (
                      <Button
                        onClick={handleClearSearch}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2"
                      >
                        <Filter className="h-4 w-4" />
                        Clear Search
                      </Button>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      {selectedAppForConnection && (
        <PipedreamConnector
          app={selectedAppForConnection}
          open={showStreamlinedConnector}
          onOpenChange={setShowStreamlinedConnector}
          onComplete={handleConnectionComplete}
          mode={mode === 'profile-only' ? 'profile-only' : 'full'}
          agentId={currentAgentId}
          saveMode={isHomePage ? 'direct' : 'callback'}
        />
      )}

      {selectedToolsProfile && currentAgentId && (
        <ToolsManager
          mode="pipedream"
          agentId={currentAgentId}
          profileId={selectedToolsProfile.profileId}
          appName={selectedToolsProfile.appName}
          profileName={selectedToolsProfile.profileName}
          open={showToolsManager}
          onOpenChange={(open) => {
            setShowToolsManager(open);
            if (!open) {
              setSelectedToolsProfile(null);
            }
          }}
          onToolsUpdate={(enabledTools) => {
            queryClient.invalidateQueries({ queryKey: ['agent', currentAgentId] });
          }}
          versionData={effectiveVersionData}
          versionId={versionId}
        />
      )}
    </div>
  );
}; ```

### 6. Agent Tools Configuration

#### File: frontend/src/components/agents/agent-tools-configuration.tsx

```typescript
import React, { useState } from 'react';
import { Search, Settings2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DEFAULT_AGENTPRESS_TOOLS, getToolDisplayName } from './tools';

interface AgentToolsConfigurationProps {
  tools: Record<string, { enabled: boolean; description: string }>;
  onToolsChange: (tools: Record<string, { enabled: boolean; description: string }>) => void;
}

export const AgentToolsConfiguration = ({ tools, onToolsChange }: AgentToolsConfigurationProps) => {
  const [searchQuery, setSearchQuery] = useState<string>('');

  const handleToolToggle = (toolName: string, enabled: boolean) => {
    const updatedTools = {
      ...tools,
      [toolName]: {
        ...tools[toolName],
        enabled
      }
    };
    onToolsChange(updatedTools);
  };

  const getSelectedToolsCount = (): number => {
    return Object.values(tools).filter(tool => tool.enabled).length;
  };

  const getFilteredTools = (): Array<[string, any]> => {
    let toolEntries = Object.entries(DEFAULT_AGENTPRESS_TOOLS);

    if (searchQuery) {
      toolEntries = toolEntries.filter(([toolName, toolInfo]) =>
        getToolDisplayName(toolName).toLowerCase().includes(searchQuery.toLowerCase()) ||
        toolInfo.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return toolEntries;
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex-shrink-0 mb-4">
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm text-muted-foreground">
            {getSelectedToolsCount()} selected
          </span>
        </div>
      </div>
      <div className="flex-1 overflow-y-auto">
        <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
          {getFilteredTools().map(([toolName, toolInfo]) => (
            <div
              key={toolName}
              className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg border hover:border-border/80 transition-colors"
            >
              <div className={`w-10 h-10 rounded-lg ${toolInfo.color} flex items-center justify-center flex-shrink-0`}>
                <span className="text-lg">{toolInfo.icon}</span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="font-medium text-sm">
                    {getToolDisplayName(toolName)}
                  </h4>
                  <Switch
                    checked={tools[toolName]?.enabled || false}
                    onCheckedChange={(checked) => handleToolToggle(toolName, checked)}
                    className="flex-shrink-0"
                  />
                </div>
                <p className="text-xs text-muted-foreground leading-relaxed">
                  {toolInfo.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {getFilteredTools().length === 0 && (
          <div className="text-center py-8">
            <div className="text-4xl mb-3">🔍</div>
            <h3 className="text-sm font-medium mb-1">No tools found</h3>
            <p className="text-xs text-muted-foreground">Try adjusting your search criteria</p>
          </div>
        )}
      </div>
    </div>
  );
}; ```

### 7. Agent MCP Configuration

#### File: frontend/src/components/agents/agent-mcp-configuration.tsx

```typescript
import React from 'react';
import { MCPConfigurationNew } from './mcp/mcp-configuration-new';

interface AgentMCPConfigurationProps {
  configuredMCPs: any[];
  customMCPs: any[];
  onMCPChange: (updates: { configured_mcps: any[]; custom_mcps: any[] }) => void;
  agentId?: string;
  versionData?: {
    configured_mcps?: any[];
    custom_mcps?: any[];
    system_prompt?: string;
    agentpress_tools?: any;
  };
  saveMode?: 'direct' | 'callback';
  versionId?: string;
}

export const AgentMCPConfiguration: React.FC<AgentMCPConfigurationProps> = ({
  configuredMCPs,
  customMCPs,
  onMCPChange,
  agentId,
  versionData,
  saveMode = 'direct',
  versionId
}) => {
  const allMCPs = [
    ...(configuredMCPs || []),
    ...(customMCPs || []).map(customMcp => ({
      name: customMcp.name,
      qualifiedName: `custom_${customMcp.type || customMcp.customType}_${customMcp.name.replace(' ', '_').toLowerCase()}`,
      config: customMcp.config,
      enabledTools: customMcp.enabledTools,
      isCustom: true,
      customType: customMcp.type || customMcp.customType
    }))
  ];

  const handleConfigurationChange = (mcps: any[]) => {
    const configured = mcps.filter(mcp => !mcp.isCustom);
    const custom = mcps
      .filter(mcp => mcp.isCustom)
      .map(mcp => ({
        name: mcp.name,
        type: mcp.customType,
        customType: mcp.customType,
        config: mcp.config,
        enabledTools: mcp.enabledTools
      }));

    onMCPChange({
      configured_mcps: configured,
      custom_mcps: custom
    });
  };

  return (
    <MCPConfigurationNew
      configuredMCPs={allMCPs}
      onConfigurationChange={handleConfigurationChange}
      agentId={agentId}
      versionData={versionData}
      saveMode={saveMode}
      versionId={versionId}
    />
  );
}; ```

### 8. Knowledge Base Manager

#### File: frontend/src/components/agents/knowledge-base/agent-knowledge-base-manager.tsx

```typescript
'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Plus,
  Edit2,
  Trash2,
  Clock,
  MoreVertical,
  AlertCircle,
  FileText,
  Eye,
  EyeOff,
  Globe,
  Search,
  Loader2,
  Bot,
  Upload,
  GitBranch,
  Archive,
  CheckCircle,
  XCircle,
  RefreshCw,
  File as FileIcon,
  BookOpen,
  PenTool,
  X
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  useAgentKnowledgeBaseEntries,
  useCreateAgentKnowledgeBaseEntry,
  useUpdateKnowledgeBaseEntry,
  useDeleteKnowledgeBaseEntry,
  useUploadAgentFiles,
  useCloneGitRepository,
  useAgentProcessingJobs,
} from '@/hooks/react-query/knowledge-base/use-knowledge-base-queries';
import { cn, truncateString } from '@/lib/utils';
import { CreateKnowledgeBaseEntryRequest, KnowledgeBaseEntry, UpdateKnowledgeBaseEntryRequest, ProcessingJob } from '@/hooks/react-query/knowledge-base/types';
import { toast } from 'sonner';
import JSZip from 'jszip';

import {
  SiJavascript,
  SiTypescript,
  SiPython,
  SiReact,
  SiHtml5,
  SiCss3,
  SiJson,
  SiMarkdown,
  SiYaml,
  SiXml
} from 'react-icons/si';
import {
  FaFilePdf,
  FaFileWord,
  FaFileExcel,
  FaFileImage,
  FaFileArchive,
  FaFileCode,
  FaFileAlt,
  FaFile
} from 'react-icons/fa';

interface AgentKnowledgeBaseManagerProps {
  agentId: string;
  agentName: string;
}

interface EditDialogData {
  entry?: KnowledgeBaseEntry;
  isOpen: boolean;
}

interface UploadedFile {
  file: File;
  id: string;
  status: 'pending' | 'uploading' | 'success' | 'error' | 'extracting';
  error?: string;
  isFromZip?: boolean;
  zipParentId?: string;
  originalPath?: string;
}

const USAGE_CONTEXT_OPTIONS = [
  {
    value: 'always',
    label: 'Always Active',
    icon: Globe,
    color: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
  },
] as const;

const getFileTypeIcon = (filename: string, mimeType?: string) => {
  const extension = filename.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'js':
      return SiJavascript;
    case 'ts':
      return SiTypescript;
    case 'jsx':
    case 'tsx':
      return SiReact;
    case 'py':
      return SiPython;
    case 'html':
      return SiHtml5;
    case 'css':
      return SiCss3;
    case 'json':
      return SiJson;
    case 'md':
      return SiMarkdown;
    case 'yaml':
    case 'yml':
      return SiYaml;
    case 'xml':
      return SiXml;
    case 'pdf':
      return FaFilePdf;
    case 'doc':
    case 'docx':
      return FaFileWord;
    case 'xls':
    case 'xlsx':
    case 'csv':
      return FaFileExcel;
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
    case 'webp':
    case 'ico':
      return FaFileImage;
    case 'zip':
    case 'rar':
    case '7z':
    case 'tar':
    case 'gz':
      return FaFileArchive;
    default:
      if (['java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala'].includes(extension || '')) {
        return FaFileCode;
      }
      if (['txt', 'rtf', 'log'].includes(extension || '')) {
        return FaFileAlt;
      }
      return FaFile;
  }
};

const getFileIconColor = (filename: string) => {
  const extension = filename.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'js':
      return 'text-yellow-500';
    case 'ts':
    case 'tsx':
      return 'text-blue-500';
    case 'jsx':
      return 'text-cyan-500';
    case 'py':
      return 'text-green-600';
    case 'html':
      return 'text-orange-600';
    case 'css':
      return 'text-blue-600';
    case 'json':
      return 'text-yellow-600';
    case 'md':
      return 'text-gray-700 dark:text-gray-300';
    case 'yaml':
    case 'yml':
      return 'text-red-500';
    case 'xml':
      return 'text-orange-500';
    case 'pdf':
      return 'text-red-600';
    case 'doc':
    case 'docx':
      return 'text-blue-700';
    case 'xls':
    case 'xlsx':
    case 'csv':
      return 'text-green-700';
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
    case 'webp':
    case 'ico':
      return 'text-purple-500';
    case 'zip':
    case 'rar':
    case '7z':
    case 'tar':
    case 'gz':
      return 'text-yellow-700';
    default:
      return 'text-gray-500';
  }
};

const getSourceIcon = (sourceType: string, filename?: string) => {
  switch (sourceType) {
    case 'file':
      return filename ? getFileTypeIcon(filename) : FileIcon;
    case 'git_repo':
      return GitBranch;
    case 'zip_extracted':
      return Archive;
    default:
      return FileText;
  }
};

const AgentKnowledgeBaseSkeleton = () => (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <div className="relative w-full">
        <Skeleton className="h-10 w-full" />
      </div>
      <Skeleton className="h-10 w-32 ml-4" />
    </div>

    <div className="space-y-3">
      {[1, 2, 3].map((i) => (
        <div key={i} className="border rounded-lg p-4">
          <div className="flex items-start justify-between gap-3">
            <div className="flex-1 min-w-0 space-y-2">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-5 w-48" />
                <Skeleton className="h-5 w-20" />
              </div>
              <Skeleton className="h-4 w-64" />
              <div className="space-y-1">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-4 w-20" />
                </div>
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
      ))}
    </div>
  </div>
);

export const AgentKnowledgeBaseManager = ({ agentId, agentName }: AgentKnowledgeBaseManagerProps) => {
  const [editDialog, setEditDialog] = useState<EditDialogData>({ isOpen: false });
  const [deleteEntryId, setDeleteEntryId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [addDialogTab, setAddDialogTab] = useState<'manual' | 'files' | 'repo'>('manual');
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState<CreateKnowledgeBaseEntryRequest>({
    name: '',
    description: '',
    content: '',
    usage_context: 'always',
  });

  const { data: knowledgeBase, isLoading, error } = useAgentKnowledgeBaseEntries(agentId);
  const { data: processingJobsData } = useAgentProcessingJobs(agentId);
  const createMutation = useCreateAgentKnowledgeBaseEntry();
  const updateMutation = useUpdateKnowledgeBaseEntry();
  const deleteMutation = useDeleteKnowledgeBaseEntry();
  const uploadMutation = useUploadAgentFiles();
  const cloneMutation = useCloneGitRepository();

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files);
    }
  }, []);

  const handleOpenAddDialog = (tab: 'manual' | 'files' | 'repo' = 'manual') => {
    setAddDialogTab(tab);
    setAddDialogOpen(true);
    setFormData({
      name: '',
      description: '',
      content: '',
      usage_context: 'always',
    });
    setUploadedFiles([]);
  };

  const handleOpenEditDialog = (entry: KnowledgeBaseEntry) => {
    setFormData({
      name: entry.name,
      description: entry.description || '',
      content: entry.content,
      usage_context: entry.usage_context,
    });
    setEditDialog({ entry, isOpen: true });
  };

  const handleCloseDialog = () => {
    setEditDialog({ isOpen: false });
    setAddDialogOpen(false);
    setFormData({
      name: '',
      description: '',
      content: '',
      usage_context: 'always',
    });
    setUploadedFiles([]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.content.trim()) {
      return;
    }

    try {
      if (editDialog.entry) {
        const updateData: UpdateKnowledgeBaseEntryRequest = {
          name: formData.name !== editDialog.entry.name ? formData.name : undefined,
          description: formData.description !== editDialog.entry.description ? formData.description : undefined,
          content: formData.content !== editDialog.entry.content ? formData.content : undefined,
          usage_context: formData.usage_context !== editDialog.entry.usage_context ? formData.usage_context : undefined,
        };
        const hasChanges = Object.values(updateData).some(value => value !== undefined);
        if (hasChanges) {
          await updateMutation.mutateAsync({ entryId: editDialog.entry.entry_id, data: updateData });
        }
      } else {
        await createMutation.mutateAsync({ agentId, data: formData });
      }

      handleCloseDialog();
    } catch (error) {
      console.error('Error saving agent knowledge base entry:', error);
    }
  };

  const handleDelete = async (entryId: string) => {
    try {
      await deleteMutation.mutateAsync(entryId);
      setDeleteEntryId(null);
    } catch (error) {
      console.error('Error deleting agent knowledge base entry:', error);
    }
  };

  const handleToggleActive = async (entry: KnowledgeBaseEntry) => {
    try {
      await updateMutation.mutateAsync({
        entryId: entry.entry_id,
        data: { is_active: !entry.is_active }
      });
    } catch (error) {
      console.error('Error toggling entry status:', error);
    }
  };

  const extractZipFile = async (zipFile: File, zipId: string) => {
    try {
      setUploadedFiles(prev => prev.map(f =>
        f.id === zipId ? { ...f, status: 'extracting' } : f
      ));

      const zip = new JSZip();
      const zipContent = await zip.loadAsync(zipFile);
      const extractedFiles: UploadedFile[] = [];

      for (const [path, file] of Object.entries(zipContent.files)) {
        if (!file.dir && !path.startsWith('__MACOSX/') && !path.includes('/.')) {
          try {
            const blob = await file.async('blob');
            const fileName = path.split('/').pop() || path;
            const extractedFile = new File([blob], fileName);

            extractedFiles.push({
              file: extractedFile,
              id: Math.random().toString(36).substr(2, 9),
              status: 'pending' as const,
              isFromZip: true,
              zipParentId: zipId,
              originalPath: path
            });
          } catch (error) {
            console.warn(`Failed to extract ${path}:`, error);
          }
        }
      }

      setUploadedFiles(prev => [
        ...prev.map(f => f.id === zipId ? { ...f, status: 'success' as const } : f),
        ...extractedFiles
      ]);

      toast.success(`Extracted ${extractedFiles.length} files from ${zipFile.name}`);
    } catch (error) {
      console.error('Error extracting ZIP:', error);
      setUploadedFiles(prev => prev.map(f =>
        f.id === zipId ? {
          ...f,
          status: 'error',
          error: 'Failed to extract ZIP file'
        } : f
      ));
      toast.error('Failed to extract ZIP file');
    }
  };

  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const newFiles: UploadedFile[] = [];

    for (const file of Array.from(files)) {
      const fileId = Math.random().toString(36).substr(2, 9);
      const uploadedFile: UploadedFile = {
        file,
        id: fileId,
        status: 'pending'
      };

      newFiles.push(uploadedFile);
      if (file.name.toLowerCase().endsWith('.zip')) {
        setTimeout(() => extractZipFile(file, fileId), 100);
      }
    }

    setUploadedFiles(prev => [...prev, ...newFiles]);
    if (!addDialogOpen) {
      setAddDialogTab('files');
      setAddDialogOpen(true);
    }
  };

  const uploadFiles = async () => {
    const filesToUpload = uploadedFiles.filter(f =>
      f.status === 'pending' &&
      (f.isFromZip || !f.file.name.toLowerCase().endsWith('.zip'))
    );
    for (const uploadedFile of filesToUpload) {
      try {
        setUploadedFiles(prev => prev.map(f =>
          f.id === uploadedFile.id ? { ...f, status: 'uploading' as const } : f
        ));

        await uploadMutation.mutateAsync({ agentId, file: uploadedFile.file });

        setUploadedFiles(prev => prev.map(f =>
          f.id === uploadedFile.id ? { ...f, status: 'success' as const } : f
        ));
      } catch (error) {
        setUploadedFiles(prev => prev.map(f =>
          f.id === uploadedFile.id ? {
            ...f,
            status: 'error' as const,
            error: error instanceof Error ? error.message : 'Upload failed'
          } : f
        ));
      }
    }

    setTimeout(() => {
      const nonZipFiles = uploadedFiles.filter(f => !f.file.name.toLowerCase().endsWith('.zip') || f.isFromZip);
      if (nonZipFiles.every(f => f.status === 'success')) {
        handleCloseDialog();
      }
    }, 1000);
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const getUsageContextConfig = (context: string) => {
    return USAGE_CONTEXT_OPTIONS.find(option => option.value === context) || USAGE_CONTEXT_OPTIONS[0];
  };

  const getJobStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return CheckCircle;
      case 'failed':
        return XCircle;
      case 'processing':
        return RefreshCw;
      default:
        return Clock;
    }
  };

  const getJobStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'processing':
        return 'text-blue-600';
      default:
        return 'text-yellow-600';
    }
  };

  if (isLoading) {
    return <AgentKnowledgeBaseSkeleton />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-sm text-red-600 dark:text-red-400">Failed to load agent knowledge base</p>
        </div>
      </div>
    );
  }

  const entries = knowledgeBase?.entries || [];
  const processingJobs = processingJobsData?.jobs || [];
  const filteredEntries = entries.filter(entry =>
    entry.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    entry.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (entry.description && entry.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <div
      className="space-y-6"
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      {dragActive && (
        <div className="fixed inset-0 bg-blue-500/20 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-900 rounded-lg p-8 shadow-lg border-2 border-dashed border-blue-500">
            <Upload className="h-12 w-12 text-blue-500 mx-auto mb-4" />
            <p className="text-lg font-medium text-center">Drop files here to upload</p>
            <p className="text-sm text-muted-foreground text-center mt-2">
              Supports documents, images, code files, and ZIP archives
            </p>
          </div>
        </div>
      )}
      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search knowledge entries..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <Button onClick={() => handleOpenAddDialog()} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Knowledge
        </Button>
      </div>
      {entries.length === 0 ? (
        <div className="text-center py-12 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border">
          <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4 border">
            <Bot className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-sm font-semibold mb-2">No Agent Knowledge Entries</h3>
          <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
            Add knowledge entries to provide <span className="font-medium">{agentName}</span> with specialized context,
            guidelines, and information it should always remember.
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {filteredEntries.length === 0 ? (
            <div className="text-center py-8">
              <Search className="h-8 w-8 mx-auto text-muted-foreground/50 mb-2" />
              <p className="text-sm text-muted-foreground">No entries match your search</p>
            </div>
          ) : (
            filteredEntries.map((entry) => {
              const contextConfig = getUsageContextConfig(entry.usage_context);
              const ContextIcon = contextConfig.icon;
              const SourceIcon = getSourceIcon(entry.source_type || 'manual', entry.source_metadata?.filename);

              return (
                <Card
                  key={entry.entry_id}
                  className={cn(
                    "group transition-all p-0",
                    entry.is_active
                      ? "bg-card"
                      : "bg-muted/30 opacity-70"
                  )}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1 min-w-0 space-y-2">
                        <div className="flex items-center gap-2">
                          <SourceIcon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          <h3 className="font-medium truncate">{entry.name}</h3>
                          {!entry.is_active && (
                            <Badge variant="outline" className="text-xs">
                              <EyeOff className="h-3 w-3 mr-1" />
                              Disabled
                            </Badge>
                          )}
                          {entry.source_type && entry.source_type !== 'manual' && (
                            <Badge variant="outline" className="text-xs">
                              {entry.source_type === 'git_repo' ? 'Git' :
                               entry.source_type === 'zip_extracted' ? 'ZIP' : 'File'}
                            </Badge>
                          )}
                        </div>
                        {entry.description && (
                          <p className="text-sm text-muted-foreground line-clamp-1">
                            {entry.description}
                          </p>
                        )}
                        <p className="text-sm text-foreground/80 line-clamp-2 leading-relaxed">
                          {entry.content}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Badge variant="outline" className={cn("text-xs gap-1", contextConfig.color)}>
                              <ContextIcon className="h-3 w-3" />
                              {contextConfig.label}
                            </Badge>
                            <span className="text-xs text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {new Date(entry.created_at).toLocaleDateString()}
                            </span>
                            {entry.file_size && (
                              <span className="text-xs text-muted-foreground">
                                {(entry.file_size / 1024).toFixed(1)}KB
                              </span>
                            )}
                          </div>
                          {entry.content_tokens && (
                            <span className="text-xs text-muted-foreground">
                              ~{entry.content_tokens.toLocaleString()} tokens
                            </span>
                          )}
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-36">
                          <DropdownMenuItem onClick={() => handleOpenEditDialog(entry)}>
                            <Edit2 className="h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleToggleActive(entry)}>
                            {entry.is_active ? (
                              <>
                                <EyeOff className="h-4 w-4" />
                                Disable
                              </>
                            ) : (
                              <>
                                <Eye className="h-4 w-4" />
                                Enable
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => setDeleteEntryId(entry.entry_id)}
                            className="text-destructive focus:bg-destructive/10 focus:text-destructive"
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>
      )}

      {/* Processing Jobs */}
      {processingJobs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Processing Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {processingJobs.map((job) => {
              const StatusIcon = getJobStatusIcon(job.status);
              const statusColor = getJobStatusColor(job.status);

              return (
                <div key={job.job_id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <StatusIcon className={cn("h-4 w-4", statusColor, job.status === 'processing' && 'animate-spin')} />
                    <div>
                      <p className="text-sm font-medium">
                        {job.job_type === 'file_upload' ? 'File Upload' :
                         job.job_type === 'git_clone' ? 'Git Repository' : 'Processing'}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {job.source_info.filename || job.source_info.git_url || 'Unknown source'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={job.status === 'completed' ? 'default' :
                                 job.status === 'failed' ? 'destructive' : 'secondary'} className="text-xs">
                      {job.status}
                    </Badge>
                    {job.status === 'completed' && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {job.entries_created} entries created
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </CardContent>
        </Card>
      )}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        onChange={(e) => handleFileUpload(e.target.files)}
        className="hidden"
        accept=".txt,.md,.py,.js,.ts,.html,.css,.json,.yaml,.yml,.xml,.csv,.pdf,.docx,.xlsx,.png,.jpg,.jpeg,.gif,.zip"
      />
      <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-blue-600" />
              Add Knowledge to {agentName}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto">
            <Tabs value={addDialogTab} onValueChange={(value) => setAddDialogTab(value as any)} className="w-full">
              <TabsList className="grid w-80 grid-cols-2">
                <TabsTrigger value="manual" className="gap-2">
                  <PenTool className="h-4 w-4" />
                  Write Knowledge
                </TabsTrigger>
                <TabsTrigger value="files" className="gap-2">
                  <Upload className="h-4 w-4" />
                  Upload Files
                  {uploadedFiles.length > 0 && (
                    <Badge variant="outline" className="ml-1">
                      {uploadedFiles.length}
                    </Badge>
                  )}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="manual" className="space-y-6 mt-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-medium">Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Coding Standards, Domain Knowledge, API Guidelines"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="usage_context" className="text-sm font-medium">Usage Context</Label>
                    <Select
                      value={formData.usage_context}
                      onValueChange={(value: 'always' | 'on_request' | 'contextual') =>
                        setFormData(prev => ({ ...prev, usage_context: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {USAGE_CONTEXT_OPTIONS.map((option) => {
                          const Icon = option.icon;
                          return (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex items-center gap-2">
                                <Icon className="h-4 w-4" />
                                <span>{option.label}</span>
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm font-medium">Description</Label>
                    <Input
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Brief description of this knowledge (optional)"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="content" className="text-sm font-medium">Content *</Label>
                    <Textarea
                      id="content"
                      value={formData.content}
                      onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                      placeholder={`Enter the specialized knowledge that ${agentName} should know...`}
                      className="min-h-[200px] resize-y"
                      required
                    />
                    <div className="text-xs text-muted-foreground">
                      Approximately {Math.ceil(formData.content.length / 4).toLocaleString()} tokens
                    </div>
                  </div>

                  <div className="flex justify-end gap-3 pt-4 border-t">
                    <Button type="button" variant="outline" onClick={handleCloseDialog}>
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={!formData.name.trim() || !formData.content.trim() || createMutation.isPending}
                      className="gap-2"
                    >
                      {createMutation.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Plus className="h-4 w-4" />
                      )}
                      Add Knowledge
                    </Button>
                  </div>
                </form>
              </TabsContent>

              <TabsContent value="files" className="space-y-6 mt-6">
                <div className="space-y-4">
                  {uploadedFiles.length === 0 && (
                    <div className="border-2 border-dashed border-border rounded-lg p-8 text-center">
                      <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-medium mb-2">Upload Files</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Drag and drop files here or click to browse.<br />
                        Supports: Documents, Code, ZIP archives
                      </p>
                      <Button
                        onClick={() => fileInputRef.current?.click()}
                        variant="outline"
                        className="gap-2"
                      >
                        <Upload className="h-4 w-4" />
                        Choose Files
                      </Button>
                    </div>
                  )}
                  {uploadedFiles.length > 0 && (
                    <div className="space-y-6">
                      {uploadedFiles.filter(f => f.file.name.toLowerCase().endsWith('.zip') && !f.isFromZip).map((zipFile) => {
                        const extractedFiles = uploadedFiles.filter(f => f.zipParentId === zipFile.id);
                        return (
                          <div key={zipFile.id} className="space-y-3">
                            {extractedFiles.length > 0 && (
                              <div>
                                <p className="text-sm font-medium text-muted-foreground mb-3">
                                  Extracted Files ({extractedFiles.length}):
                                </p>
                                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                  {extractedFiles.map((extractedFile) => {
                                    const ExtractedFileIcon = getFileTypeIcon(extractedFile.file.name);
                                    const iconColor = getFileIconColor(extractedFile.file.name);
                                    return (
                                      <div key={extractedFile.id} className="group relative p-2 pb-0 rounded-lg border bg-muted flex items-center">
                                        <div className="flex items-center text-center space-y-2">
                                          <ExtractedFileIcon className={cn("h-8 w-8", iconColor)} />
                                          <div className="w-full flex flex-col items-start ml-2">
                                            <p className="text-xs font-medium truncate" title={extractedFile.file.name}>
                                              {extractedFile.file.name}
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                              {(extractedFile.file.size / 1024).toFixed(1)}KB
                                            </p>
                                          </div>
                                          <div className="absolute top-1 right-1">
                                            {extractedFile.status === 'uploading' && (
                                              <Loader2 className="h-3 w-3 animate-spin text-blue-600" />
                                            )}
                                            {extractedFile.status === 'success' && (
                                              <CheckCircle className="h-3 w-3 text-green-600" />
                                            )}
                                            {extractedFile.status === 'error' && (
                                              <XCircle className="h-3 w-3 text-red-600" />
                                            )}
                                            {extractedFile.status === 'pending' && (
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => removeFile(extractedFile.id)}
                                                className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                              >
                                                <X className="h-3 w-3" />
                                              </Button>
                                            )}
                                          </div>
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      })}
                      {uploadedFiles.filter(f => !f.isFromZip && !f.file.name.toLowerCase().endsWith('.zip')).length > 0 && (
                        <div className="space-y-3">
                          <p className="text-sm font-medium text-muted-foreground">
                            Individual Files ({uploadedFiles.filter(f => !f.isFromZip && !f.file.name.toLowerCase().endsWith('.zip')).length}):
                          </p>
                          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                            {uploadedFiles.filter(f => !f.isFromZip && !f.file.name.toLowerCase().endsWith('.zip')).map((uploadedFile) => {
                              const FileTypeIcon = getFileTypeIcon(uploadedFile.file.name);
                              const iconColor = getFileIconColor(uploadedFile.file.name);
                              return (
                                <div key={uploadedFile.id} className="group relative p-2 pb-0 rounded-lg border bg-muted flex items-center">
                                  <div className="flex items-center text-center space-y-2">
                                    <FileTypeIcon className={cn("h-8 w-8", iconColor)} />
                                    <div className="w-full flex flex-col items-start ml-2">
                                      <p className="text-xs font-medium truncate" title={uploadedFile.file.name}>
                                        {truncateString(uploadedFile.file.name, 20)}
                                      </p>
                                      <p className="text-xs text-muted-foreground">
                                        {(uploadedFile.file.size / 1024).toFixed(1)}KB
                                      </p>
                                    </div>
                                    <div className="absolute top-1 right-1">
                                      {uploadedFile.status === 'uploading' && (
                                        <Loader2 className="h-3 w-3 animate-spin text-blue-600" />
                                      )}
                                      {uploadedFile.status === 'success' && (
                                        <CheckCircle className="h-3 w-3 text-green-600" />
                                      )}
                                      {uploadedFile.status === 'error' && (
                                        <XCircle className="h-3 w-3 text-red-600" />
                                      )}
                                      {uploadedFile.status === 'pending' && (
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => removeFile(uploadedFile.id)}
                                          className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                        >
                                          <X className="h-3 w-3" />
                                        </Button>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {uploadedFiles.length > 0 && (
                    <div className="flex justify-end gap-3 pt-4 border-t">
                      <Button type="button" variant="outline" onClick={handleCloseDialog}>
                        Cancel
                      </Button>
                      <Button
                        onClick={uploadFiles}
                        disabled={uploadMutation.isPending || uploadedFiles.filter(f =>
                          f.status === 'pending' &&
                          (f.isFromZip || !f.file.name.toLowerCase().endsWith('.zip'))
                        ).length === 0}
                        className="gap-2"
                      >
                        {uploadMutation.isPending ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Upload className="h-4 w-4" />
                        )}
                        Upload Files ({uploadedFiles.filter(f =>
                          f.status === 'pending' &&
                          (f.isFromZip || !f.file.name.toLowerCase().endsWith('.zip'))
                        ).length})
                      </Button>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>
      <Dialog open={editDialog.isOpen} onOpenChange={handleCloseDialog}>
        <DialogContent className="max-w-2xl max-h-[85vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Edit2 className="h-5 w-5 text-blue-600" />
              Edit Knowledge Entry
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto">
            <form onSubmit={handleSubmit} className="space-y-6 p-1">
              <div className="space-y-2">
                <Label htmlFor="edit-name" className="text-sm font-medium">Name *</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Coding Standards, Domain Knowledge, API Guidelines"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-usage_context" className="text-sm font-medium">Usage Context</Label>
                <Select
                  value={formData.usage_context}
                  onValueChange={(value: 'always' | 'on_request' | 'contextual') =>
                    setFormData(prev => ({ ...prev, usage_context: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {USAGE_CONTEXT_OPTIONS.map((option) => {
                      const Icon = option.icon;
                      return (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <Icon className="h-4 w-4" />
                            <span>{option.label}</span>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-description" className="text-sm font-medium">Description</Label>
                <Input
                  id="edit-description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Brief description of this knowledge (optional)"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-content" className="text-sm font-medium">Content *</Label>
                <Textarea
                  id="edit-content"
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder={`Enter the specialized knowledge that ${agentName} should know...`}
                  className="min-h-[200px] resize-y"
                  required
                />
                <div className="text-xs text-muted-foreground">
                  Approximately {Math.ceil(formData.content.length / 4).toLocaleString()} tokens
                </div>
              </div>

              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button type="button" variant="outline" onClick={handleCloseDialog}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={!formData.name.trim() || !formData.content.trim() || updateMutation.isPending}
                  className="gap-2"
                >
                  {updateMutation.isPending ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Edit2 className="h-4 w-4" />
                  )}
                  Save Changes
                </Button>
              </div>
            </form>
          </div>
        </DialogContent>
      </Dialog>
      <AlertDialog open={!!deleteEntryId} onOpenChange={() => setDeleteEntryId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Delete Knowledge Entry
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this knowledge entry. {agentName} will no longer have access to this information.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteEntryId && handleDelete(deleteEntryId)}
              className="bg-destructive hover:bg-destructive/90"
            >
              Delete Entry
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}; ```

### 9. Triggers Configuration

#### File: frontend/src/components/agents/triggers/agent-triggers-configuration.tsx

```typescript
"use client";

import React, { useState } from 'react';
import { Zap } from 'lucide-react';
import { Dialog } from '@/components/ui/dialog';
import { ConfiguredTriggersList } from './configured-triggers-list';
import { TriggerConfigDialog } from './trigger-config-dialog';
import { TriggerConfiguration, TriggerProvider } from './types';
import {
  useAgentTriggers,
  useCreateTrigger,
  useUpdateTrigger,
  useDeleteTrigger,
  useToggleTrigger,
  useTriggerProviders
} from '@/hooks/react-query/triggers';
import { toast } from 'sonner';
import { OneClickIntegrations } from './one-click-integrations';

interface AgentTriggersConfigurationProps {
  agentId: string;
}


export const AgentTriggersConfiguration: React.FC<AgentTriggersConfigurationProps> = ({
  agentId,
}) => {
  const [configuringProvider, setConfiguringProvider] = useState<TriggerProvider | null>(null);
  const [editingTrigger, setEditingTrigger] = useState<TriggerConfiguration | null>(null);

  const { data: triggers = [], isLoading, error } = useAgentTriggers(agentId);
  const { data: providers = [] } = useTriggerProviders();
  const createTriggerMutation = useCreateTrigger();
  const updateTriggerMutation = useUpdateTrigger();
  const deleteTriggerMutation = useDeleteTrigger();
  const toggleTriggerMutation = useToggleTrigger();

  const handleEditTrigger = (trigger: TriggerConfiguration) => {
    setEditingTrigger(trigger);

    const provider = providers.find(p => p.provider_id === trigger.provider_id);
    if (provider) {
      setConfiguringProvider(provider);
    } else {
      setConfiguringProvider({
        provider_id: trigger.provider_id,
        name: trigger.trigger_type,
        description: '',
        trigger_type: trigger.trigger_type,
        webhook_enabled: !!trigger.webhook_url,
        config_schema: {}
      });
    }
  };

  const handleRemoveTrigger = async (trigger: TriggerConfiguration) => {
    try {
      await deleteTriggerMutation.mutateAsync({
        triggerId: trigger.trigger_id,
        agentId: trigger.agent_id
      });
      toast.success('Trigger deleted successfully');
    } catch (error) {
      toast.error('Failed to delete trigger');
      console.error('Error deleting trigger:', error);
    }
  };

  const handleSaveTrigger = async (config: any) => {
    try {
      if (editingTrigger) {
        await updateTriggerMutation.mutateAsync({
          triggerId: editingTrigger.trigger_id,
          name: config.name,
          description: config.description,
          config: config.config,
          is_active: config.is_active,
        });
        toast.success('Trigger updated successfully');
      } else {
        await createTriggerMutation.mutateAsync({
          agentId,
          provider_id: configuringProvider!.provider_id,
          name: config.name,
          description: config.description,
          config: config.config,
        });
        toast.success('Trigger created successfully');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to save trigger');
      console.error('Error saving trigger:', error);
    }
    setConfiguringProvider(null);
    setEditingTrigger(null);
  };

  const handleToggleTrigger = async (trigger: TriggerConfiguration) => {
    try {
      await toggleTriggerMutation.mutateAsync({
        triggerId: trigger.trigger_id,
        isActive: !trigger.is_active,
      });
      toast.success(`Trigger ${!trigger.is_active ? 'enabled' : 'disabled'}`);
    } catch (error) {
      toast.error('Failed to toggle trigger');
      console.error('Error toggling trigger:', error);
    }
  };

  if (error) {
    return (
      <div className="rounded-xl p-6 border border-destructive/20 bg-destructive/5">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-destructive/10 rounded-lg">
            <Zap className="h-5 w-5 text-destructive" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-destructive">Error Loading Triggers</h3>
            <p className="text-sm text-muted-foreground">
              {error instanceof Error ? error.message : 'Failed to load triggers'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-y-auto space-y-4">
        <OneClickIntegrations agentId={agentId} />

        {triggers.length > 0 && (
          <ConfiguredTriggersList
            triggers={triggers}
            onEdit={handleEditTrigger}
            onRemove={handleRemoveTrigger}
            onToggle={handleToggleTrigger}
            isLoading={deleteTriggerMutation.isPending || toggleTriggerMutation.isPending}
          />
        )}

        {!isLoading && triggers.length === 0 && (
          <div className="text-center py-12 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border">
            <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4 border">
              <Zap className="h-6 w-6 text-muted-foreground" />
            </div>
            <h4 className="text-sm font-semibold text-foreground">
              No triggers configured
            </h4>
            <p className="text-sm text-muted-foreground mb-6 max-w-sm mx-auto">
              Click on a trigger provider above to get started
            </p>
          </div>
        )}
      </div>

      {configuringProvider && (
        <Dialog open={!!configuringProvider} onOpenChange={() => setConfiguringProvider(null)}>
          <TriggerConfigDialog
            provider={configuringProvider}
            existingConfig={editingTrigger}
            onSave={handleSaveTrigger}
            onCancel={() => setConfiguringProvider(null)}
            isLoading={createTriggerMutation.isPending || updateTriggerMutation.isPending}
            agentId={agentId}
          />
        </Dialog>
      )}
    </div>
  );
}; ```

### 10. Workflows Configuration

#### File: frontend/src/components/agents/workflows/agent-workflows-configuration.tsx

```typescript
'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Play, Pause, AlertCircle, Workflow, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import {
  useAgentWorkflows,
  useCreateAgentWorkflow,
  useUpdateAgentWorkflow,
  useDeleteAgentWorkflow,
  useExecuteWorkflow,
} from '@/hooks/react-query/agents/use-agent-workflows';
import {
  AgentWorkflow
} from '@/hooks/react-query/agents/workflow-utils';

interface AgentWorkflowsConfigurationProps {
  agentId: string;
  agentName: string;
}

export function AgentWorkflowsConfiguration({ agentId, agentName }: AgentWorkflowsConfigurationProps) {
  const router = useRouter();

  const { data: workflows = [], isLoading } = useAgentWorkflows(agentId);
  const createWorkflowMutation = useCreateAgentWorkflow();
  const updateWorkflowMutation = useUpdateAgentWorkflow();
  const deleteWorkflowMutation = useDeleteAgentWorkflow();
  const executeWorkflowMutation = useExecuteWorkflow();

  const [isExecuteDialogOpen, setIsExecuteDialogOpen] = useState(false);
  const [workflowToExecute, setWorkflowToExecute] = useState<AgentWorkflow | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [workflowToDelete, setWorkflowToDelete] = useState<AgentWorkflow | null>(null);
  const [activeTab, setActiveTab] = useState('workflows');

  const [executionInput, setExecutionInput] = useState<string>('');

  const handleCreateWorkflow = useCallback(async () => {
    try {
      const defaultWorkflow = {
        name: 'Untitled Workflow',
        description: 'A new workflow',
        steps: []
      };
      const newWorkflow = await createWorkflowMutation.mutateAsync({
        agentId,
        workflow: defaultWorkflow
      });
      router.push(`/agents/config/${agentId}/workflow/${newWorkflow.id}`);
    } catch (error) {
      toast.error('Failed to create workflow');
    }
  }, [agentId, router, createWorkflowMutation]);

  const handleUpdateWorkflowStatus = useCallback(async (workflowId: string, status: AgentWorkflow['status']) => {
    await updateWorkflowMutation.mutateAsync({
      agentId,
      workflowId,
      workflow: { status }
    });
  }, [agentId, updateWorkflowMutation]);

  const handleExecuteWorkflow = useCallback((workflow: AgentWorkflow) => {
    setWorkflowToExecute(workflow);
    setIsExecuteDialogOpen(true);
  }, []);

  const handleWorkflowClick = useCallback((workflowId: string) => {
    router.push(`/agents/config/${agentId}/workflow/${workflowId}`);
  }, [agentId, router]);

  const handleDeleteWorkflow = useCallback((workflow: AgentWorkflow) => {
    setWorkflowToDelete(workflow);
    setIsDeleteDialogOpen(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!workflowToDelete) return;

    try {
      await deleteWorkflowMutation.mutateAsync({ agentId, workflowId: workflowToDelete.id });
      toast.success('Workflow deleted successfully');
      setIsDeleteDialogOpen(false);
      setWorkflowToDelete(null);
    } catch (error) {
      toast.error('Failed to delete workflow');
    }
  }, [agentId, workflowToDelete, deleteWorkflowMutation]);

  const handleConfirmExecution = useCallback(async () => {
    if (!workflowToExecute) return;

    try {
      const result = await executeWorkflowMutation.mutateAsync({
        agentId,
        workflowId: workflowToExecute.id,
        execution: {
          input_data: executionInput.trim() ? { prompt: executionInput } : undefined
        }
      });

      setIsExecuteDialogOpen(false);
      setWorkflowToExecute(null);
      setExecutionInput('');

      toast.success(`${result.message}`);
    } catch (error) {
      toast.error('Failed to execute workflow');
    }
  }, [agentId, workflowToExecute, executionInput, executeWorkflowMutation]);



  const getStatusBadge = (status: AgentWorkflow['status']) => {
    const colors = {
      draft: 'text-gray-700 bg-gray-100',
      active: 'text-green-700 bg-green-100',
      paused: 'text-yellow-700 bg-yellow-100',
      archived: 'text-red-700 bg-red-100'
    };

    return (
      <Badge className={colors[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex-shrink-0 mb-4">
        <Button
          size='sm'
          variant='outline'
          className="flex items-center gap-2"
          onClick={handleCreateWorkflow}
          disabled={createWorkflowMutation.isPending}
        >
          <Plus className="h-4 w-4" />
          {createWorkflowMutation.isPending ? 'Creating...' : 'Create Workflow'}
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsContent value="workflows" className="space-y-4">
            {isLoading ? (
              <div className="flex items-center justify-center p-8">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 animate-spin" />
                  <span>Loading workflows...</span>
                </div>
              </div>
            ) : workflows.length === 0 ? (
              <div className="text-center py-12 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border">
                <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4 border">
                  <Workflow className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-sm font-semibold mb-2">No Agent Workflows</h3>
                <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
                  Create workflows to automate tasks and streamline your agent's operations.
                </p>
              </div>
            ) : (
              <div className="grid gap-4">
                {workflows.map((workflow) => (
                  <Card
                    key={workflow.id}
                    className="p-4 cursor-pointer hover:opacity-80 transition-colors"
                    onClick={() => handleWorkflowClick(workflow.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3">
                          <h4 className="font-semibold">{workflow.name}</h4>
                          {getStatusBadge(workflow.status)}
                          {workflow.is_default && <Badge variant="outline">Default</Badge>}
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">{workflow.description}</p>
                        <div className="flex items-center gap-4 mt-2">
                          <span className="text-xs text-muted-foreground">
                            Created {new Date(workflow.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleExecuteWorkflow(workflow);
                          }}
                          disabled={workflow.status !== 'active' || executeWorkflowMutation.isPending}
                        >
                          <Play className="h-4 w-4" />
                          Execute
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUpdateWorkflowStatus(
                              workflow.id,
                              workflow.status === 'active' ? 'paused' : 'active'
                            );
                          }}
                          disabled={updateWorkflowMutation.isPending}
                        >
                          {workflow.status === 'active' ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteWorkflow(workflow);
                          }}
                          disabled={deleteWorkflowMutation.isPending}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      <Dialog open={isExecuteDialogOpen} onOpenChange={setIsExecuteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Execute Workflow</DialogTitle>
            <DialogDescription>
              Provide input data for "{workflowToExecute?.name}" workflow
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>What would you like the workflow to work on?</Label>
              <Textarea
                value={executionInput}
                onChange={(e) => setExecutionInput(e.target.value)}
                placeholder="Enter your request..."
                rows={4}
                className="resize-none"
                required={true}
              />
            </div>

            <div className="flex items-center justify-between pt-4">
              <Button
                variant="outline"
                onClick={() => setIsExecuteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirmExecution}
                disabled={executeWorkflowMutation.isPending}
              >
                {executeWorkflowMutation.isPending ? 'Executing...' : 'Execute Workflow'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Workflow</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete workflow {workflowToDelete?.name}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteWorkflowMutation.isPending}
            >
              {deleteWorkflowMutation.isPending ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} ```

---

## End of Documentation

This documentation contains all the files and dependencies required for the Chat Input Configuration Panel component. The panel provides a streamlined interface for configuring agent settings directly from the chat interface.
````
