# PRD: Agent Template Selection Sign-Up Flow

## Feature Overview

Implement a new sign-up flow where users select from 4 pre-built agent templates before Google OAuth authentication. After sign-up, users are guided through connecting required MCP tools for their selected agent.

## User Journey

1. User clicks "Login/Sign up with Google" button
2. Glass-morphism popup appears with 4 agent template options
3. User selects an agent template
4. User is redirected to Google OAuth
5. After successful auth, user lands on dashboard
6. Selected template is automatically installed
7. MCP connection modal appears for required integrations
8. User connects required tools
9. Agent is ready for use with first prompt

## Technical Requirements

### UI Components

- Liquid glass button component for Google sign-in
- Glass-morphism popup overlay
- Agent template cards with:
  - Agent avatar and name
  - Description
  - Required integrations with MCP icons
  - Glass card aesthetic

### Data Flow

1. Fetch public templates from `agent_templates` table
2. Store selected `template_id` in sessionStorage
3. Pass template through OAuth redirect
4. Install template after authentication
5. Configure MCP connections

## Implementation Task List

### Phase 1: UI Components Setup ✅

- [x] Install liquid glass button component using shadcn
- [x] Create AgentTemplateSelector popup component with glass morphism
- [x] Design agent template cards with glass aesthetic
- [x] Implement template selection state management

### Phase 2: Authentication Flow

- [x] Modify GoogleSignIn component to accept selected template
- [x] Add template persistence through OAuth flow
- [x] Update auth callback to handle template data
- [x] Create session storage utilities for template selection

### Phase 3: Post-Auth Setup ✅

- [x] Add dashboard query parameter handling for template
- [x] Implement automatic template installation after auth
- [x] Create agent setup completion flow
- [x] Handle MCP connection requirements modal

### Phase 4: Integration & Testing ✅

- [x] Connect all flows end-to-end
- [x] Add error handling for edge cases
- [x] Create public API endpoint for templates
- [x] Update frontend to use public API
- [ ] Test complete user journey

## Technical Details

### Components to Create

1. **AgentTemplateSelector.tsx**

   - Glass popup component
   - Template card grid
   - Selection handling

2. **AgentTemplateCard.tsx**
   - Individual template display
   - Integration icons
   - Selection state

### Components to Modify

1. **GoogleSignIn.tsx**

   - Add template selection prop
   - Handle pre-auth template selection

2. **auth/callback/route.ts**

   - Retrieve template from session
   - Pass to dashboard redirect

3. **dashboard-content.tsx**
   - Handle template query param
   - Auto-install template
   - Show MCP modal

### API Endpoints Used

- `GET /api/marketplace/templates` - Fetch public templates
- `POST /api/marketplace/install` - Install selected template
- Composio MCP endpoints for tool connections

### State Management

- SessionStorage for template persistence
- React state for UI interactions
- Query parameters for post-auth flow

## Success Metrics

- Seamless sign-up to first prompt experience
- Clear visual hierarchy in template selection
- Successful template installation rate
- MCP connection completion rate

## Edge Cases

- User closes popup without selecting
- OAuth fails after template selection
- Template installation fails
- Required MCP connections unavailable
- User already has account

## Future Enhancements

- Remember last selected template
- Template recommendations based on use case
- Skip template selection for returning users
- Custom template creation flow
