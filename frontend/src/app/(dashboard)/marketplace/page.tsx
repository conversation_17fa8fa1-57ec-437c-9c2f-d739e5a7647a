'use client';

import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { Search, Download, Star, Calendar, User, TrendingUp, CheckCircle, Loader2, Settings, AlertTriangle, GitBranch, Plus, ShoppingBag, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Sheet, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';
import { getAgentAvatar } from '../agents/_utils/get-agent-style';
import { Skeleton } from '@/components/ui/skeleton';
import { InstallDialogComposio } from './_components/install-dialog-composio';
import { getComposioAppIcon, ToolCase } from '@/lib/icon-mapping';
import { MCPAppsModal } from '@/components/dashboard/mcp-apps-modal';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import {
  useMarketplaceTemplates,
  useInstallTemplate
} from '@/hooks/react-query/secure-mcp/use-secure-mcp';
import { useComposioConnections, useComposioApps } from '@/hooks/react-query/composio/use-composio';
import { ComposioMCPService } from '@/lib/composio-api';
import { useMCPConnection } from '@/hooks/use-mcp-connection';
import { useDefaultAgentMCPs } from '@/hooks/react-query/agents/use-agents';
import { useFeatureFlag } from '@/lib/feature-flags';
import { useRouter } from 'next/navigation';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';

type SortOption = 'newest' | 'popular' | 'most_downloaded' | 'name';

interface MarketplaceTemplate {
  id: string;
  name: string;
  description: string;
  tags: string[];
  download_count: number;
  creator_name: string;
  created_at: string;
  marketplace_published_at?: string;
  avatar?: string;
  avatar_color?: string;
  template_id: string;
  is_kortix_team?: boolean;
  mcp_requirements?: Array<{
    qualified_name: string;
    display_name: string;
    enabled_tools?: string[];
    required_config: string[];
    custom_type?: 'sse' | 'http';
  }>;
  metadata?: {
    source_agent_id?: string;
    source_version_id?: string;
    source_version_name?: string;
  };
}

interface AgentPreviewSheetProps {
  item: MarketplaceTemplate | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onInstall: (item: MarketplaceTemplate) => void;
  isInstalling: boolean;
}

const AgentPreviewSheet: React.FC<AgentPreviewSheetProps> = ({
  item,
  open,
  onOpenChange,
  onInstall,
  isInstalling
}) => {
  if (!item) return null;

  const { avatar, color } = item.avatar && item.avatar_color
    ? { avatar: item.avatar, color: item.avatar_color }
    : getAgentAvatar(item.id);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent>
        <SheetHeader className="space-y-4">
          <div className="flex items-start gap-4">
            <div
              className="h-16 w-16 flex items-center justify-center rounded-xl shrink-0"
              style={{ backgroundColor: color }}
            >
              <div className="text-3xl">{avatar}</div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <SheetTitle className="text-xl font-semibold line-clamp-2">
                  {item.name}
                </SheetTitle>
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Download className="h-4 w-4" />
                  <span>{item.download_count} downloads</span>
                </div>
              </div>
            </div>
          </div>
          <Button
            onClick={() => onInstall(item)}
            disabled={isInstalling}
            size='sm'
            className='w-48'
          >
            {isInstalling ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Installing...
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                Add to Library
              </>
            )}
          </Button>
        </SheetHeader>
        <div className="px-4 space-y-6 py-6">
          <div className="space-y-2">
            <h3 className="font-medium text-xs text-muted-foreground uppercase tracking-wide">
              Description
            </h3>
            <p className="text-sm leading-relaxed">
              {item.description || 'No description available for this agent.'}
            </p>
          </div>

          {item.tags && item.tags.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {item.tags.map(tag => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          {item.mcp_requirements && item.mcp_requirements.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium text-xs text-muted-foreground uppercase tracking-wide">
                Required Apps & Tools
              </h3>
              <div className="space-y-2">
                {item.mcp_requirements.map((mcp, index) => {
                  // Extract app key from qualified name for Composio apps
                  const isComposioApp = mcp.qualified_name.startsWith('composio/');
                  const appKey = isComposioApp ? mcp.qualified_name.replace('composio/', '') : null;

                  // Get icon component
                  let IconComponent = ToolCase;
                  if (isComposioApp && appKey) {
                    IconComponent = getComposioAppIcon({ key: appKey, name: mcp.display_name }) as any;
                  }

                  return (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted-foreground/10 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-background border border-border/20 overflow-hidden">
                          {React.createElement(IconComponent, { className: "h-5 w-5" })}
                        </div>
                        <div>
                          <div className="font-medium text-sm">{mcp.display_name}</div>
                          {mcp.enabled_tools && mcp.enabled_tools.length > 0 && (
                            <div className="text-xs text-muted-foreground">
                              {mcp.enabled_tools.length} tool{mcp.enabled_tools.length !== 1 ? 's' : ''}
                            </div>
                          )}
                        </div>
                      </div>
                      {mcp.custom_type && (
                        <Badge variant="outline" className="text-xs">
                          {mcp.custom_type.toUpperCase()}
                        </Badge>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
          {item.metadata?.source_version_name && (
            <div className="space-y-2">
              <h3 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                Version
              </h3>
              <div className="flex items-center gap-2">
                <GitBranch className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{item.metadata.source_version_name}</span>
              </div>
            </div>
          )}
          {item.marketplace_published_at && (
            <div className="space-y-2">
              <h3 className="font-medium text-xs text-muted-foreground uppercase tracking-wide">
                Published
              </h3>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{formatDate(item.marketplace_published_at)}</span>
              </div>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
};


export default function MarketplacePage() {
  const { enabled: agentMarketplaceEnabled, loading: flagLoading } = useFeatureFlag("agent_marketplace");
  const router = useRouter();
  const isMobile = useIsMobile();
  const { setOpenMobile } = useSidebar();

  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [selectedItem, setSelectedItem] = useState<MarketplaceTemplate | null>(null);
  const [showPreviewSheet, setShowPreviewSheet] = useState(false);
  const [showInstallDialog, setShowInstallDialog] = useState(false);
  const [installingItemId, setInstallingItemId] = useState<string | null>(null);

  // State for two-step installation flow
  const [showAppsModal, setShowAppsModal] = useState(false);
  const [pendingInstallItem, setPendingInstallItem] = useState<MarketplaceTemplate | null>(null);
  const [requiredAppKeys, setRequiredAppKeys] = useState<string[]>([]);
  const [connectingApps, setConnectingApps] = useState(new Set<string>());
  const [disconnectingApps, setDisconnectingApps] = useState(new Set<string>());

  const queryParams = useMemo(() => ({
    page,
    limit: 20,
    offset: (page - 1) * 20,
    search: searchQuery || undefined,
    tags: selectedTags.length > 0 ? selectedTags.join(',') : undefined,
  }), [page, searchQuery, selectedTags]);

  const { data: secureTemplates, isLoading } = useMarketplaceTemplates(queryParams);
  const installTemplateMutation = useInstallTemplate();
  const { connections, isLoading: isLoadingConnections, refetch: refetchConnections } = useComposioConnections();
    const { apps: composioApps, isLoading: isLoadingApps } = useComposioApps();
  const { data: defaultMCPs, isLoading: isLoadingDefaultMCPs, refetch: refetchDefaultMCPs } = useDefaultAgentMCPs();

  useEffect(() => {
    if (!flagLoading && !agentMarketplaceEnabled) {
      router.replace("/dashboard");
    }
  }, [flagLoading, agentMarketplaceEnabled, router]);

  // Check for OAuth completion (similar to prompts page)
  useEffect(() => {
    const recentlyConnected = localStorage.getItem('composio_recently_connected');
    if (recentlyConnected && pendingInstallItem) {
      // User just connected an app, check if all required apps are now connected
      setTimeout(async () => {
        await refetchDefaultMCPs();
        // Will be handled by the defaultMCPs update effect
        localStorage.removeItem('composio_recently_connected');
        localStorage.removeItem('composio_connection_request_id');
        localStorage.removeItem('composio_connection_app_name');
      }, 2000);
    }
  }, [pendingInstallItem, refetchDefaultMCPs]);

  // Transform secure templates data
  const { kortixTeamItems, communityItems } = useMemo(() => {
    const kortixItems: MarketplaceTemplate[] = [];
    const communityItems: MarketplaceTemplate[] = [];

    // Add secure templates (all items are now secure)
    if (secureTemplates) {
      secureTemplates.forEach(template => {
        const item: MarketplaceTemplate = {
          id: template.template_id,
          name: template.name,
          description: template.description,
          tags: template.tags || [],
          download_count: template.download_count || 0,
          creator_name: template.creator_name || 'Anonymous',
          created_at: template.created_at,
          marketplace_published_at: template.marketplace_published_at,
          avatar: template.avatar,
          avatar_color: template.avatar_color,
          template_id: template.template_id,
          is_kortix_team: template.is_kortix_team,
          mcp_requirements: template.mcp_requirements,
          metadata: template.metadata,
        };

        if (template.is_kortix_team) {
          kortixItems.push(item);
        } else {
          communityItems.push(item);
        }
      });
    }

    // Sort function
    const sortItems = (items: MarketplaceTemplate[]) => {
      return items.sort((a, b) => {
        switch (sortBy) {
          case 'newest':
            return new Date(b.marketplace_published_at || b.created_at).getTime() -
                   new Date(a.marketplace_published_at || a.created_at).getTime();
          case 'popular':
          case 'most_downloaded':
            return b.download_count - a.download_count;
          case 'name':
            return a.name.localeCompare(b.name);
          default:
            return 0;
        }
      });
    };

    return {
      kortixTeamItems: sortItems(kortixItems),
      communityItems: sortItems(communityItems)
    };
  }, [secureTemplates, sortBy]);

  // Combined items for tag filtering and search stats
  const allMarketplaceItems = useMemo(() => {
    return [...kortixTeamItems, ...communityItems];
  }, [kortixTeamItems, communityItems]);

  React.useEffect(() => {
    setPage(1);
  }, [searchQuery, selectedTags, sortBy]);

  // Check if pending install can proceed when connections update
  useEffect(() => {
    console.log('[Marketplace] Pending install effect:', {
      hasPendingItem: !!pendingInstallItem,
      isLoadingDefaultMCPs,
      defaultMCPs
    });

    if (pendingInstallItem && !isLoadingDefaultMCPs) {
      const missingApps = getMissingAppsForTemplate(pendingInstallItem);
      if (missingApps.length === 0) {
        console.log('[Marketplace] All apps connected after OAuth, proceeding to install dialog');
        // All apps connected, proceed with install dialog
        setShowAppsModal(false);
        setSelectedItem(pendingInstallItem);
        setShowInstallDialog(true);
        setPendingInstallItem(null);
      } else {
        console.log('[Marketplace] Still missing apps after OAuth:', missingApps);
      }
    }
  }, [defaultMCPs, pendingInstallItem, isLoadingDefaultMCPs]);

  // Helper function to get missing apps for a template
  const getMissingAppsForTemplate = (template: MarketplaceTemplate): string[] => {
    console.log('[Marketplace] Checking missing apps for template:', template.name);
    console.log('[Marketplace] Template MCP requirements:', template.mcp_requirements);
    console.log('[Marketplace] Default MCPs data:', defaultMCPs);

    if (!template.mcp_requirements) {
      console.log('[Marketplace] Template has no MCP requirements');
      return [];
    }

    if (!defaultMCPs) {
      console.log('[Marketplace] Default MCPs not loaded yet');
      return [];
    }

    // Get required Composio apps from template
    console.log('[Marketplace] All MCP requirements:', template.mcp_requirements.map(req => ({
      qualified_name: req.qualified_name,
      display_name: req.display_name,
      custom_type: req.custom_type
    })));

    // All MCP requirements in marketplace templates are Composio apps
    // They are stored with just the app key as qualified_name (e.g., "gmail", "google_sheets")
    const requiredApps = template.mcp_requirements
      .map(req => ({
        appKey: req.qualified_name,
        displayName: req.display_name
      }));

    console.log('[Marketplace] Required Composio apps:', requiredApps);

    // Check against default agent's custom_mcps
    const customMcps = defaultMCPs.custom_mcps || [];
    console.log('[Marketplace] Default agent custom_mcps:', customMcps);

    // Check if an app is connected by looking at the custom_mcps
    const isAppConnected = (appKey: string): boolean => {
      const result = customMcps.some((mcp: any) => {
        // Check if it's a Composio MCP (has mcp.composio.dev URL)
        const isComposioMcp = mcp.config?.url?.includes('mcp.composio.dev');
        // Check if the name matches (case-insensitive)
        // Backend stores as title case (e.g., "Gmail", "Google Sheets")
        // Also check without replacing underscores for single-word apps
        const nameMatches = mcp.name?.toLowerCase() === appKey.replace('_', ' ').toLowerCase() ||
                           mcp.name?.toLowerCase() === appKey.toLowerCase();

        console.log(`[Marketplace] Checking MCP:`, {
          mcpName: mcp.name,
          mcpUrl: mcp.config?.url,
          appKey: appKey,
          appKeyFormatted: appKey.replace('_', ' '),
          isComposioMcp,
          nameMatches,
          matches: isComposioMcp && nameMatches
        });

        return isComposioMcp && nameMatches;
      });

      console.log(`[Marketplace] App ${appKey} connected: ${result}`);
      return result;
    };

    const missingApps = requiredApps
      .filter(app => !isAppConnected(app.appKey))
      .map(app => app.appKey);

    console.log('[Marketplace] Missing apps:', missingApps);
    return missingApps;
  };

  // Remove external filtering - let MCPAppsModal handle it internally

  // Connected apps set for the modal
  const connectedAppsSet = useMemo(() => {
    return new Set(
      connections
        .filter(conn => conn.status === 'connected')
        .map(conn => conn.app_key)
    );
  }, [connections]);

  // MCP connection hook for OAuth flow
  const { connectToMCPServer } = useMCPConnection({
    onConnectionSuccess: async (appKey, appName) => {
      console.log(`Successfully connected ${appName}`);
      // Refetch default MCPs to update the UI
      await refetchDefaultMCPs();
    },
    onConnectionError: (appKey, appName, error) => {
      console.error(`Failed to connect ${appName}:`, error);
    }
  });

  // Handle connection for OAuth flow
  const handleConnect = useCallback(async (appKey: string, appName: string) => {
    if (!composioApps || connectingApps.has(appKey)) return;

    const app = composioApps.find(a => a.key === appKey);
    if (!app) {
      console.error(`App not found: ${appKey}`);
      return;
    }

    setConnectingApps(prev => new Set(prev).add(appKey));

    try {
      // Store context for post-OAuth
      localStorage.setItem('composio_connection_request_id', crypto.randomUUID());
      localStorage.setItem('composio_connection_app_name', app.name);

      await connectToMCPServer(app);
    } finally {
      setConnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  }, [composioApps, connectingApps, connectToMCPServer]);

  // Handle disconnect
  const handleDisconnect = useCallback(async (appKey: string, appName: string) => {
    if (disconnectingApps.has(appKey)) return;

    setDisconnectingApps(prev => new Set(prev).add(appKey));

    try {
      await ComposioMCPService.deleteConnection(appKey);
      await refetchConnections();
    } catch (error) {
      console.error(`Failed to disconnect ${appKey}:`, error);
    } finally {
      setDisconnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  }, [disconnectingApps, refetchConnections]);

  const handleItemClick = (item: MarketplaceTemplate) => {
    setSelectedItem(item);
    setShowPreviewSheet(true);
  };

  const handlePreviewInstall = (item: MarketplaceTemplate) => {
    console.log('[Marketplace] handlePreviewInstall called for:', item.name);
    setShowPreviewSheet(false);
    // Check for missing apps first
    const missingApps = getMissingAppsForTemplate(item);
    console.log('[Marketplace] Missing apps for preview install:', missingApps);

    if (missingApps.length > 0) {
      console.log('[Marketplace] Showing apps modal for missing apps');
      // Show apps modal first
      setPendingInstallItem(item);
      setRequiredAppKeys(missingApps);
      setShowAppsModal(true);
    } else {
      console.log('[Marketplace] All apps connected, showing install dialog');
      // All apps connected, proceed directly to install dialog
      setShowInstallDialog(true);
    }
  };

    const handleInstallClick = (item: MarketplaceTemplate, e?: React.MouseEvent) => {
    console.log('[Marketplace] handleInstallClick called for:', item.name);
    if (e) {
      e.stopPropagation();
    }
    setSelectedItem(item);

    // Check for missing apps first
    const missingApps = getMissingAppsForTemplate(item);
    console.log('[Marketplace] Missing apps for install click:', missingApps);

    if (missingApps.length > 0) {
      console.log('[Marketplace] Showing apps modal for missing apps');
      // Show apps modal first
      setPendingInstallItem(item);
      setRequiredAppKeys(missingApps);
      setShowAppsModal(true);
    } else {
      console.log('[Marketplace] All apps connected, showing install dialog');
      // All apps connected, proceed directly to install dialog
      setShowInstallDialog(true);
    }
  };

  const handleInstall = async (
    item: MarketplaceTemplate,
    instanceName?: string
  ) => {
    setInstallingItemId(item.id);

    try {
      if (!instanceName || instanceName.trim() === '') {
        toast.error('Please provide a name for the agent');
        return;
      }

      // Build custom MCP configs for Composio apps
      const customMcpConfigs: Record<string, any> = {};

      // Get connected Composio apps from default agent's custom_mcps
      if (item.mcp_requirements && defaultMCPs?.custom_mcps) {
        const customMcps = defaultMCPs.custom_mcps || [];

        // All MCP requirements are Composio apps
        item.mcp_requirements
          .forEach(req => {
            const appKey = req.qualified_name; // Already just the app key

            // Find the matching MCP in default agent's custom_mcps
            const matchingMcp = customMcps.find((mcp: any) => {
              const isComposioMcp = mcp.config?.url?.includes('mcp.composio.dev');
              const nameMatches = mcp.name?.toLowerCase() === appKey.replace('_', ' ').toLowerCase() ||
                                 mcp.name?.toLowerCase() === appKey.toLowerCase();
              return isComposioMcp && nameMatches;
            });

            if (matchingMcp) {
              // Use "composio/{appKey}" format for the backend
              customMcpConfigs[`composio/${req.qualified_name}`] = {
                name: req.display_name,
                type: req.custom_type || 'sse',
                config: { url: matchingMcp.config.url },
                enabledTools: req.enabled_tools || []
              };
            }
          });
      }

      const result = await installTemplateMutation.mutateAsync({
        template_id: item.template_id,
        instance_name: instanceName,
        profile_mappings: {}, // Will be removed in backend update
        custom_mcp_configs: customMcpConfigs
      });

      if (result.status === 'installed') {
        toast.success(`Agent "${instanceName}" installed successfully!`);
        setShowInstallDialog(false);
        // Navigate to the agents page
        router.push('/agents');
      } else if (result.status === 'configs_required') {
        toast.error('Please connect all required apps');
        return;
      } else {
        toast.error('Unexpected response from server. Please try again.');
        return;
      }
    } catch (error: any) {
      console.error('Installation error:', error);

      // Handle specific error types
      if (error.message?.includes('already in your library')) {
        toast.error('This agent is already in your library');
      } else if (error.message?.includes('Template not found')) {
        toast.error('This agent template is no longer available');
      } else if (error.message?.includes('Access denied')) {
        toast.error('You do not have permission to install this agent');
      } else {
        toast.error(error.message || 'Failed to install agent. Please try again.');
      }
    } finally {
      setInstallingItemId(null);
    }
  };

  const handleTagFilter = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const getItemStyling = (item: MarketplaceTemplate) => {
    if (item.avatar && item.avatar_color) {
      return {
        avatar: item.avatar,
        color: item.avatar_color,
      };
    }
    return getAgentAvatar(item.id);
  };

  const allTags = React.useMemo(() => {
    const tags = new Set<string>();
    allMarketplaceItems.forEach(item => {
      item.tags?.forEach(tag => tags.add(tag));
    });
    return Array.from(tags);
  }, [allMarketplaceItems]);

  if (flagLoading) {
    return (
      <div className="container max-w-7xl mx-auto px-4 py-8">
        <div className="flex justify-between items-center">
          <div className="space-y-2">
            <h1 className="text-2xl font-semibold tracking-tight text-foreground">
              Marketplace
            </h1>
            <p className="text-md text-muted-foreground max-w-2xl">
              Discover and install secure AI agent templates created by the community
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search agents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortOption)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Newest First
                </div>
              </SelectItem>
              <SelectItem value="popular">
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4" />
                  Most Popular
                </div>
              </SelectItem>
              <SelectItem value="most_downloaded">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Most Downloaded
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  }

  if (!agentMarketplaceEnabled) {
    return null;
  }

  return (
    <div className="container mx-auto max-w-7xl px-4 py-8">
      {isMobile && (
        <div className="absolute top-4 left-4 z-10">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setOpenMobile(true)}
              >
                <Menu className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>Open menu</TooltipContent>
          </Tooltip>
        </div>
      )}

      <div className="space-y-8">
        <div className='w-full space-y-4 bg-gradient-to-b from-primary/10 to-primary/5 border rounded-xl h-60 flex items-center justify-center'>
          <div className="space-y-4">
            <div className="space-y-2 text-center">
              <div className='flex items-center justify-center gap-2'>
                <ShoppingBag className='h-6 w-6 text-primary' />
                <h1 className="text-2xl font-semibold tracking-tight text-foreground">
                  Marketplace
                </h1>
              </div>
              <p className="text-md text-muted-foreground max-w-2xl">
                Discover and install powerful agents created by the community
              </p>
            </div>
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
              <div className="relative flex-1 border rounded-xl">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search agents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              {/* <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortOption)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Newest First
                    </div>
                  </SelectItem>
                  <SelectItem value="popular">
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4" />
                      Most Popular
                    </div>
                  </SelectItem>
                  <SelectItem value="most_downloaded">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Most Downloaded
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select> */}
            </div>
          </div>
        </div>


        <div className="text-sm text-muted-foreground">
          {isLoading ? (
            "Loading marketplace..."
          ) : (
            `${allMarketplaceItems.length} template${allMarketplaceItems.length !== 1 ? 's' : ''} found`
          )}
        </div>

        {isLoading ? (
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="bg-neutral-100 dark:bg-sidebar border border-border rounded-2xl overflow-hidden">
                <Skeleton className="h-50" />
                <div className="p-4 space-y-3">
                  <Skeleton className="h-5 rounded" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 rounded" />
                    <Skeleton className="h-4 rounded w-3/4" />
                  </div>
                  <Skeleton className="h-8" />
                </div>
              </div>
            ))}
          </div>
        ) : allMarketplaceItems.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              {searchQuery || selectedTags.length > 0
                ? "No templates found matching your criteria. Try adjusting your search or filters."
                : "No agent templates are currently available in the marketplace."}
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {kortixTeamItems.length > 0 && (
              <div className="space-y-6">
                <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                  {kortixTeamItems.map((item) => {
                    const { avatar, color } = getItemStyling(item);
                    return (
                      <div
                        key={item.id}
                        className="bg-neutral-100 dark:bg-sidebar border border-border rounded-2xl overflow-hidden hover:bg-muted/50 transition-all duration-200 cursor-pointer group flex flex-col h-full"
                        onClick={() => handleItemClick(item)}
                      >
                        <div className='p-4'>
                          <div className={`h-12 w-12 flex items-center justify-center rounded-lg`} style={{ backgroundColor: color }}>
                            <div className="text-2xl">
                              {avatar}
                            </div>
                          </div>
                        </div>
                        <div className="p-4 -mt-4 flex flex-col flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="text-foreground font-medium text-lg line-clamp-1 flex-1">
                              {item.name}
                            </h3>
                            {item.metadata?.source_version_name && (
                              <Badge variant="secondary" className="text-xs shrink-0">
                                <GitBranch className="h-3 w-3" />
                                {item.metadata.source_version_name}
                              </Badge>
                            )}
                          </div>
                          <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                            {item.description || 'No description available'}
                          </p>
                          {item.tags && item.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-3">
                              {item.tags.slice(0, 2).map(tag => (
                                <Badge key={tag} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                              {item.tags.length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{item.tags.length - 2}
                                </Badge>
                              )}
                            </div>
                          )}
                          <div className="mb-4 w-full flex justify-between">
                            <div className='space-y-1'>
                              {item.marketplace_published_at && (
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  <Calendar className="h-3 w-3" />
                                  <span>{new Date(item.marketplace_published_at).toLocaleDateString()}</span>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center gap-1">
                              <Download className="h-3 w-3 text-muted-foreground" />
                              <span className="text-muted-foreground text-xs font-medium">{item.download_count}</span>
                            </div>
                          </div>
                          <Button
                            onClick={(e) => handleInstallClick(item, e)}
                            disabled={installingItemId === item.id}
                            className="w-full transition-opacity mt-auto"
                            size="sm"
                          >
                            {installingItemId === item.id ? (
                              <>
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Installing...
                              </>
                            ) : (
                              <>
                                <Download className="h-4 w-4" />
                                Add to Library
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
            {communityItems.length > 0 && (
              <div className="space-y-6">
                <div className="flex items-center gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
                    <User className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-foreground">Default Agent</h2>
                  </div>
                </div>
                <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                  {communityItems.map((item) => {
                    const { avatar, color } = getItemStyling(item);
                    return (
                      <div
                        key={item.id}
                        className="bg-neutral-100 dark:bg-sidebar border border-border rounded-2xl overflow-hidden hover:bg-muted/50 transition-all duration-200 cursor-pointer group flex flex-col h-full"
                        onClick={() => handleItemClick(item)}
                      >
                        <div className='p-4'>
                          <div className={`h-12 w-12 flex items-center justify-center rounded-lg`} style={{ backgroundColor: color }}>
                            <div className="text-2xl">
                              {avatar}
                            </div>
                          </div>
                        </div>
                        <div className="p-4 -mt-4 flex flex-col flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="text-foreground font-medium text-lg line-clamp-1 flex-1">
                              {item.name}
                            </h3>
                            {item.metadata?.source_version_name && (
                              <Badge variant="secondary" className="text-xs shrink-0">
                                <GitBranch className="h-3 w-3" />
                                {item.metadata.source_version_name}
                              </Badge>
                            )}
                          </div>
                          <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                            {item.description || 'No description available'}
                          </p>
                          {item.tags && item.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-3">
                              {item.tags.slice(0, 2).map(tag => (
                                <Badge key={tag} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                              {item.tags.length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{item.tags.length - 2}
                                </Badge>
                              )}
                            </div>
                          )}
                          <div className="mb-4 w-full flex justify-between">
                            <div className='space-y-1'>
                              {item.marketplace_published_at && (
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  <Calendar className="h-3 w-3" />
                                  <span>{new Date(item.marketplace_published_at).toLocaleDateString()}</span>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center gap-1">
                              <Download className="h-3 w-3 text-muted-foreground" />
                              <span className="text-muted-foreground text-xs font-medium">{item.download_count}</span>
                            </div>
                          </div>
                          <Button
                            onClick={(e) => handleInstallClick(item, e)}
                            disabled={installingItemId === item.id}
                            className="w-full transition-opacity mt-auto"
                            size="sm"
                          >
                            {installingItemId === item.id ? (
                              <>
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Installing...
                              </>
                            ) : (
                              <>
                                <Download className="h-4 w-4" />
                                Add to Library
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      <AgentPreviewSheet
        item={selectedItem}
        open={showPreviewSheet}
        onOpenChange={setShowPreviewSheet}
        onInstall={handlePreviewInstall}
        isInstalling={installingItemId === selectedItem?.id}
      />
      <InstallDialogComposio
        item={selectedItem}
        open={showInstallDialog}
        onOpenChange={setShowInstallDialog}
        onInstall={handleInstall}
        isInstalling={installingItemId === selectedItem?.id}
      />
      {/* Apps connection modal - shown before install dialog */}
      <MCPAppsModal
        isOpen={showAppsModal}
        onClose={() => {
          setShowAppsModal(false);
          // Clear pending install if user closes modal
          setPendingInstallItem(null);
          setRequiredAppKeys([]);
        }}
        apps={composioApps || []}
        connectedApps={connectedAppsSet}
        onConnect={handleConnect}
        onDisconnect={handleDisconnect}
        connectingApps={connectingApps}
        disconnectingApps={disconnectingApps}
        filterAppKeys={requiredAppKeys}
        filterTitle="Connect Required Apps"
        filterDescription={`Connect the following apps to use this agent template:`}
        isLoading={isLoadingApps}
      />
    </div>
  );
}
