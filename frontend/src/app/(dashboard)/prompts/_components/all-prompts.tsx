'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardTitle } from '@/components/ui/card'
import { Search, Lock } from 'lucide-react'
import {
  SiGmail, SiNotion, SiLinear, SiHubspot, SiFigma, SiClickup, SiGooglesheets, SiGoogledocs,
  SiSlack, SiSalesforce, SiAirtable, SiZoom, SiReddit, SiGoogledrive
} from 'react-icons/si'
import { FaMicrosoft, FaTwitter } from 'react-icons/fa'
import {
  getMissingIntegrationAppKeys,
  PersonalizedPromptRecommendation
} from '@/lib/utils/personalized-prompt-system'
import { INTEGRATION_DISPLAY_NAMES, getUserConnectedIntegrations, getUserEmail } from '@/lib/utils/integration-checker'
import { INITIAL_PROMPTS, ADVANCED_PROMPTS, personalizePromptQuery, getMissingIntegrationsForPrompt } from '@/lib/utils/personalized-prompts'

// Integration icon mapping
const getIntegrationIcon = (appKey: string) => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    'gmail': SiGmail,
    'google_sheets': SiGooglesheets,
    'google_calendar': SiGooglesheets, // Using sheets icon as fallback for calendar
    'google_docs': SiGoogledocs,
    'google_drive': SiGoogledrive,
    'notion': SiNotion,
    'slack': SiSlack,
    'teams': FaMicrosoft,
    'twitter': FaTwitter,
    'linear': SiLinear,
    'clickup': SiClickup,
    'hubspot': SiHubspot,
    'salesforce': SiSalesforce,
    'airtable': SiAirtable,
    'zoom': SiZoom,
    'outlook': FaMicrosoft, // Using Microsoft icon for Outlook
    'reddit': SiReddit,
    'findanyone': Search, // Using Search icon for FindAnyone
  }

  return iconMap[appKey] || Search
}

// Personalized card component
const PersonalizedExampleCard = ({
  recommendation,
  index,
  onPromptClick
}: {
  recommendation: PersonalizedPromptRecommendation
  index: number
  onPromptClick: (recommendation: PersonalizedPromptRecommendation) => void
}) => {
  // Helper function to get the first sentence from the query
  const getFirstSentence = (query: string) => {
    const match = query.match(/^[^.!?]*[.!?]/)
    if (match) {
      return match[0].trim()
    }

    if (query.length <= 80) return query
    const truncated = query.substring(0, 80)
    const lastSpace = truncated.lastIndexOf(' ')
    return lastSpace > 0 ? truncated.substring(0, lastSpace) + '...' : truncated + '...'
  }

  const { prompt, canRun } = recommendation

  return (
    <motion.div
      initial={{ opacity: 1 }}
      whileHover={{
        scale: 1.02,
        y: -3,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
    >
      <Card
        className="cursor-pointer h-full bg-muted/50 dark:bg-muted/30 border border-border min-h-[45px] transition-shadow duration-300 hover:shadow-lg"
        onClick={() => onPromptClick(recommendation)}
      >
        <CardContent className="px-3 py-0.5 h-full flex flex-col space-y-0.5">
          {/* Quote snippet at the top - single line */}
          <div>
            <blockquote className="text-xs text-muted-foreground italic leading-tight truncate">
              "{getFirstSentence(recommendation.personalizedQuery)}"
            </blockquote>
          </div>

          {/* Title */}
          <CardTitle className="font-semibold text-foreground text-sm leading-tight">
            {prompt.title}
          </CardTitle>

          {/* Integration icons tray with overlapping circular design */}
          <div className="flex items-center justify-between mt-auto pt-1">
            <div className="flex items-center">
              {prompt.integrations.slice(0, 5).map((integration, idx) => {
                const IconComponent = getIntegrationIcon(integration.app_key)
                return (
                  <div
                    key={integration.app_key}
                    className="relative flex items-center justify-center bg-background rounded-full shadow-sm p-1"
                    style={{
                      height: 24,
                      width: 24,
                      marginLeft: idx > 0 ? '-8px' : '0',
                      zIndex: prompt.integrations.length - idx,
                    }}
                    title={INTEGRATION_DISPLAY_NAMES[integration.app_key] || integration.name}
                  >
                    <IconComponent className="w-3 h-3 text-muted-foreground" />
                  </div>
                )
              })}
              {prompt.integrations.length > 5 && (
                <div
                  className="relative flex items-center justify-center bg-muted rounded-full shadow-sm"
                  style={{
                    height: 24,
                    width: 24,
                    marginLeft: '-8px',
                    zIndex: 0,
                  }}
                >
                  <span className="text-xs text-muted-foreground font-medium">
                    +{prompt.integrations.length - 5}
                  </span>
                </div>
              )}
            </div>

            {!canRun && (
              <div className="flex items-center justify-center w-6 h-6">
                <Lock className="h-4 w-4 text-foreground/70" />
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export const AllPrompts = ({
  onSelectPrompt,
  onRequireIntegrations,
}: {
  onSelectPrompt?: (query: string) => void
  onRequireIntegrations?: (appKeys: string[], promptTitle: string) => void
}) => {
  const [allPrompts, setAllPrompts] = useState<PersonalizedPromptRecommendation[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Load ALL prompts on mount (both initial and advanced)
  useEffect(() => {
    loadAllPrompts()
  }, [])

  // Check for post-OAuth integration updates on mount
  useEffect(() => {
    const checkOnMount = () => {
      const recentlyConnected = localStorage.getItem('composio_recently_connected')
      if (recentlyConnected) {
        console.log('Post-OAuth detected on mount:', recentlyConnected)
        setTimeout(() => {
          loadAllPrompts()
          localStorage.removeItem('composio_recently_connected')
        }, 2000)
      }
    }

    checkOnMount()
  }, [])

  const loadAllPrompts = async () => {
    try {
      setIsLoading(true)
      
      // Get user data in parallel
      const [userEmail, userIntegrations] = await Promise.all([
        getUserEmail(),
        getUserConnectedIntegrations()
      ])
      
      // Combine ALL prompts from both collections
      const allRawPrompts = [...INITIAL_PROMPTS, ...ADVANCED_PROMPTS]
      
      // Create PersonalizedPromptRecommendation objects for each prompt
      const allPromptRecommendations: PersonalizedPromptRecommendation[] = allRawPrompts.map(prompt => {
        const personalizedQuery = userEmail ? personalizePromptQuery(prompt.query, userEmail) : prompt.query
        const missingIntegrations = getMissingIntegrationsForPrompt(prompt, userIntegrations)
        const canRun = missingIntegrations.length === 0
        
        return {
          prompt,
          personalizedQuery,
          canRun,
          missingIntegrations
        }
      })
      
      setAllPrompts(allPromptRecommendations)
    } catch (error) {
      console.error('Error loading all prompts:', error)
      setAllPrompts([])
    } finally {
      setIsLoading(false)
    }
  }

  const handlePromptClick = (recommendation: PersonalizedPromptRecommendation) => {
    if (recommendation.canRun) {
      // User can run this prompt, proceed normally
      onSelectPrompt?.(recommendation.personalizedQuery)
    } else {
      // User needs to connect integrations first
      const missingAppKeys = getMissingIntegrationAppKeys(recommendation.missingIntegrations)
      onRequireIntegrations?.(missingAppKeys, recommendation.prompt.title)
    }
  }

  return (
    <div className="w-full">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {allPrompts.map((recommendation, index) => (
          <PersonalizedExampleCard
            key={`${recommendation.prompt.id}-${index}`}
            recommendation={recommendation}
            index={index}
            onPromptClick={handlePromptClick}
          />
        ))}
      </div>
    </div>
  )
}