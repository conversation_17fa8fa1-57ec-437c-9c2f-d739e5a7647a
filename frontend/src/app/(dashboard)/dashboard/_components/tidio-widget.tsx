'use client';

import Script from 'next/script';
import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

export function TidioWidget() {
  const pathname = usePathname();
  const isDashboardPage = pathname === '/dashboard';

  useEffect(() => {
    // Add custom styles for Tidio chat positioning
    const style = document.createElement('style');
    style.textContent = `
      /* Force Tidio to bottom right with minimal spacing */
      #tidio-chat {
        position: fixed !important;
        bottom: 10px !important;
        right: 10px !important;
        z-index: 999 !important;
      }
      
      /* Target all possible Tidio containers */
      #tidio-chat > div,
      #tidio-chat-container,
      [data-test="widgetButton"] {
        position: fixed !important;
        bottom: 10px !important;
        right: 10px !important;
        margin: 0 !important;
        transform: none !important;
      }
      
      /* Make the chat button much smaller */
      #tidio-chat [data-test="widgetButton"],
      #tidio-chat button {
        width: 40px !important;
        height: 40px !important;
        transform: scale(0.85) !important;
      }
      
      /* Scale down the icon inside */
      #tidio-chat [data-test="widgetButton"] svg,
      #tidio-chat button svg {
        width: 20px !important;
        height: 20px !important;
      }
      
      /* Chat window positioning when open - also smaller */
      #tidio-chat iframe {
        bottom: 55px !important;
        right: 10px !important;
        max-width: 250px !important;
        max-height: 350px !important;
      }

      /* Hide Tidio widget when not on dashboard */
      .tidio-hidden {
        display: none !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      // Cleanup style on unmount
      if (style.parentNode) {
        document.head.removeChild(style);
      }
    };
  }, []);

  // Control Tidio widget visibility based on current route
  useEffect(() => {
    const controlTidioVisibility = () => {
      const tidioChat = document.getElementById('tidio-chat');
      if (tidioChat) {
        if (isDashboardPage) {
          tidioChat.classList.remove('tidio-hidden');
        } else {
          tidioChat.classList.add('tidio-hidden');
        }
      }
    };

    // Check immediately and then set up polling for when Tidio loads
    controlTidioVisibility();
    
    const interval = setInterval(controlTidioVisibility, 100);
    
    return () => clearInterval(interval);
  }, [isDashboardPage]);

  // Only load the script once when component mounts
  return (
    <Script
      src="//code.tidio.co/x96sodjxgetnv2aviizhgzi7fc8oxhyp.js"
      strategy="afterInteractive"
      async
    />
  );
}