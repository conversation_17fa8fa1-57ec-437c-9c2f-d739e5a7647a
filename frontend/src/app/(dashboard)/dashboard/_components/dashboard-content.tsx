'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import { <PERSON>u, Lightbulb, X, Loader2 } from 'lucide-react';
import {
  ChatInput,
  ChatInputHandles,
} from '@/components/thread/chat-input/chat-input';
import {
  BillingError,
} from '@/lib/api';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useBillingError } from '@/hooks/useBillingError';
import { BillingErrorAlert } from '@/components/billing/usage-limit-alert';
import { useAccounts } from '@/hooks/use-accounts';
import { config } from '@/lib/config';
import { useInitiateAgentWithInvalidation } from '@/hooks/react-query/dashboard/use-initiate-agent';
import { ModalProviders } from '@/providers/modal-providers';
import { BillingModal } from '@/components/billing/billing-modal';

import { cn } from '@/lib/utils';
import { useModal } from '@/hooks/use-modal-store';
import { Examples } from './suggestions/examples';
import { useThreadQuery } from '@/hooks/react-query/threads/use-threads';
import { normalizeFilenameToNFC } from '@/lib/utils/unicode';
// import { MCPIntegrations } from '@/components/dashboard/mcp-integrations'; // Deprecated
import { MCPServerCarousel } from '@/components/dashboard/mcp-server-carousel';
import { ActionCards } from '@/components/dashboard/action-cards';
import { MCPAppsModal } from '@/components/dashboard/mcp-apps-modal';
import { ComposioApp } from '@/types/composio';
import { ComposioMCPService } from '@/lib/composio-api';
import { createClient } from '@/lib/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import { agentKeys } from '@/hooks/react-query/agents/keys';

import { toast } from 'sonner';
import { useInstallTemplate, useMarketplaceTemplates } from '@/hooks/react-query/secure-mcp/use-secure-mcp';
import { useAuth } from '@/components/AuthProvider';
import { useDefaultAgentMCPs, useAgents } from '@/hooks/react-query/agents/use-agents';
import { useTemplateInstallation } from '@/hooks/use-template-installation';
import { AgentTemplateSelector } from '@/components/agent-template-selector';

const PENDING_PROMPT_KEY = 'pendingAgentPrompt';

export function DashboardContent() {
  const [inputValue, setInputValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [autoSubmit, setAutoSubmit] = useState(false);
  const [selectedAgentId, setSelectedAgentId] = useState<string | undefined>();
  const [initiatedThreadId, setInitiatedThreadId] = useState<string | null>(null);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [showBillingModal, setShowBillingModal] = useState(false);

  // Agent Template Selector State
  const [showAgentTemplateSelector, setShowAgentTemplateSelector] = useState(false);

  // MCP Apps Modal State for filtered integration requirements
  const [apps, setApps] = useState<ComposioApp[]>([]);
  const [loading, setLoading] = useState(true);
  const [connectingApps, setConnectingApps] = useState<Set<string>>(new Set());
  const [disconnectingApps, setDisconnectingApps] = useState<Set<string>>(new Set());
  const [connectedApps, setConnectedApps] = useState<Set<string>>(new Set());
  const [isFilteredModalOpen, setIsFilteredModalOpen] = useState(false);
  const [filteredAppKeys, setFilteredAppKeys] = useState<string[]>([]);
  const [modalTitle, setModalTitle] = useState<string>('');
  const [modalDescription, setModalDescription] = useState<string>('');
  const { billingError, handleBillingError, clearBillingError } =
    useBillingError();
  const router = useRouter();
  const searchParams = useSearchParams();
  const isMobile = useIsMobile();
  const { setOpenMobile, openMobile } = useSidebar();
  const { data: accounts } = useAccounts();
  const personalAccount = accounts?.find((account) => account.personal_account);
  const chatInputRef = useRef<ChatInputHandles>(null);
  const initiateAgentMutation = useInitiateAgentWithInvalidation();
  const { onOpen } = useModal();
  const queryClient = useQueryClient();

  // Template installation state - using centralized hook
  const [pendingTemplateId, setPendingTemplateId] = useState<string | null>(null);
  const templateInstallation = useTemplateInstallation();

  // Hooks for template installation
  const installTemplateMutation = useInstallTemplate();
  const { data: defaultMCPs } = useDefaultAgentMCPs();
  const { user } = useAuth();
  
  // Check if user has any agents
  const { data: agents, isLoading: agentsLoading } = useAgents();

  // Load MCP apps for filtered modal
  useEffect(() => {
    const loadApps = async () => {
      try {
        setLoading(true);
        const data = await ComposioMCPService.getSupportedApps();
        if (data.success) {
          setApps(data.apps);
        }
      } catch (error) {
        console.error('Error loading apps:', error);
      } finally {
        setLoading(false);
      }
    };

    loadApps();
  }, []);

  // Load connected apps from Composio
  useEffect(() => {
    const loadConnectedApps = async () => {
      try {
        const connections = await ComposioMCPService.listUserConnections();
        const connectedAppKeys = new Set(connections.map(conn => conn.app_key));
        setConnectedApps(connectedAppKeys);
      } catch (error) {
        console.error('Error loading connected apps:', error);
      }
    };

    loadConnectedApps();
  }, []);

  // Show agent template selector for new users
  useEffect(() => {
    if (!agentsLoading && user && agents && agents.agents.length === 0) {
      // User has no agents, show template selector
      setShowAgentTemplateSelector(true);
    }
  }, [agentsLoading, user, agents]);

  // Track timeout for cleanup
  const installTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (installTimeoutRef.current) {
        clearTimeout(installTimeoutRef.current);
      }
    };
  }, []);

  // Stable function for template installation to prevent infinite loops
  const installTemplateStable = useCallback(async (templateId: string) => {
    if (!user) return;

    // Use centralized state management to prevent duplicates
    if (!templateInstallation.startInstallation(templateId)) {
      return; // Installation already in progress or attempted
    }

    installTimeoutRef.current = setTimeout(() => {
      templateInstallation.failInstallation('Template installation timed out. Please try again.', true);
    }, 30000);

    try {
      const userName = user.user_metadata?.full_name ||
                      user.email?.split('@')[0] ||
                      'My Agent';

      const customMcpConfigs: Record<string, any> = {};

      const result = await installTemplateMutation.mutateAsync({
        template_id: templateId,
        instance_name: `${userName}'s Assistant`,
        custom_mcp_configs: customMcpConfigs
      });

      if (result.status === 'installed' && result.instance_id) {
        templateInstallation.completeInstallation(result.instance_id);
        setSelectedAgentId(result.instance_id);
        setPendingTemplateId(null);

        await queryClient.invalidateQueries({ queryKey: agentKeys.all });

        if (result.missing_custom_configs && result.missing_custom_configs.length > 0) {
          const missingAppKeys = result.missing_custom_configs.map(config => {
            // Normalize qualified_name to match Composio app.key format
            // Template stores "google_drive", but Composio uses "googledrive"
            const qualifiedName = config.qualified_name;
            
            // Remove "composio/" prefix if present
            const cleanName = qualifiedName.startsWith('composio/') ? qualifiedName.slice(9) : qualifiedName;
            
            // Convert snake_case to expected format (remove underscores)
            return cleanName.replace(/_/g, '');
          });
          setFilteredAppKeys(missingAppKeys);
          setModalTitle('Connect Required Apps');
          setModalDescription('To use this agent, please connect the following integrations:');
          setIsFilteredModalOpen(true);
        }
      } else if (result.status === 'configs_required') {
        const missingAppKeys = result.missing_custom_configs?.map(config => {
          // Normalize qualified_name to match Composio app.key format
          // Template stores "google_drive", but Composio uses "googledrive"
          const qualifiedName = config.qualified_name;
          
          // Remove "composio/" prefix if present
          const cleanName = qualifiedName.startsWith('composio/') ? qualifiedName.slice(9) : qualifiedName;
          
          // Convert snake_case to expected format (remove underscores)
          return cleanName.replace(/_/g, '');
        }) || [];
        setFilteredAppKeys(missingAppKeys);
        setModalTitle('Connect Required Apps');
        setModalDescription('To complete setup, please connect the following integrations:');
        setIsFilteredModalOpen(true);
        setPendingTemplateId(null);
        templateInstallation.completeInstallation('configs-required');
      }
    } catch (error: any) {
      console.error('Failed to install template:', error);

      const isRetryable = error?.message?.includes('NetworkError') || 
                         error?.code === 'ECONNREFUSED' || 
                         error?.status === 401;

      if (isRetryable) {
        templateInstallation.failInstallation(
          error?.status === 401 
            ? 'Authentication error. Please sign in again.'
            : 'Network error. Please check your connection and refresh the page.',
          true
        );
      } else {
        templateInstallation.failInstallation('Failed to install agent template. Please try again.');
        setPendingTemplateId(null);
      }
    } finally {
      if (installTimeoutRef.current) {
        clearTimeout(installTimeoutRef.current);
        installTimeoutRef.current = null;
      }
    }
  }, [user, templateInstallation, installTemplateMutation, queryClient, setSelectedAgentId, setFilteredAppKeys, setModalTitle, setModalDescription, setIsFilteredModalOpen]);

  // Install template when conditions are met - simplified effect
  useEffect(() => {
    if (pendingTemplateId && user && !templateInstallation.isInstalling && !templateInstallation.hasAttempted(pendingTemplateId)) {
      installTemplateStable(pendingTemplateId);
    }
  }, [pendingTemplateId, user, templateInstallation, installTemplateStable]);

  // Handle template selection from agent selector
  const handleTemplateSelect = (templateId: string) => {
    setPendingTemplateId(templateId);
    setShowAgentTemplateSelector(false);
  };


  const threadQuery = useThreadQuery(initiatedThreadId || '');

  useEffect(() => {
    const agentIdFromUrl = searchParams.get('agent_id');
    if (agentIdFromUrl && agentIdFromUrl !== selectedAgentId) {
      setSelectedAgentId(agentIdFromUrl);
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('agent_id');
      router.replace(newUrl.pathname + newUrl.search, { scroll: false });
    }
  }, [searchParams, selectedAgentId, router]);

  // Stable OAuth completion handler to prevent infinite loops
  const handleOAuthCompletion = useCallback(async (appKey: string, connectionRequestId: string, appName: string) => {
    try {
      console.log(`Processing OAuth completion for ${appKey}`);
      
      // Wait for OAuth to complete on Composio's side
      await new Promise(resolve => setTimeout(resolve, 2000));

      const statusResult = await ComposioMCPService.checkConnectionStatus(
        connectionRequestId,
        appKey as any
      );

      if (statusResult.success && statusResult.is_connected) {
        // Clear storage flags
        localStorage.removeItem('composio_recently_connected');
        localStorage.removeItem('composio_connection_request_id');
        localStorage.removeItem('composio_connection_app_name');

        // Update connected apps
        setConnectedApps(prev => new Set(prev).add(appKey));

        // Invalidate React Query cache
        queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
        queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });

        toast.success('Authentication Complete!', {
          description: `${appName} is now connected and ready to use.`
        });
      } else {
        console.log('Connection not ready yet, may need to wait longer');
      }
    } catch (error) {
      console.error('Error processing OAuth completion:', error);
    }
  }, [queryClient, setConnectedApps]);

  // Handle OAuth callback parameters from Composio
  useEffect(() => {
    const status = searchParams.get('status');
    const connectedAccountId = searchParams.get('connectedAccountId');
    const appName = searchParams.get('appName');

    if (status === 'success' && connectedAccountId && appName) {
      console.log(`OAuth callback detected for ${appName} with account ID: ${connectedAccountId}`);

      // Clean up URL parameters immediately
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('status');
      newUrl.searchParams.delete('connectedAccountId');
      newUrl.searchParams.delete('appName');
      router.replace(newUrl.pathname + newUrl.search, { scroll: false });

      // Check localStorage flags only once
      const recentlyConnectedKey = localStorage.getItem('composio_recently_connected');
      const connectionRequestId = localStorage.getItem('composio_connection_request_id');

      if (recentlyConnectedKey && connectionRequestId) {
        handleOAuthCompletion(recentlyConnectedKey, connectionRequestId, appName);
      } else {
        console.log('Missing localStorage flags for post-OAuth processing');
      }
    }
  }, [searchParams, router, handleOAuthCompletion]);

  useEffect(() => {
    if (threadQuery.data && initiatedThreadId) {
      const thread = threadQuery.data;
      console.log('Thread data received:', thread);
      if (thread.project_id) {
        router.push(`/projects/${thread.project_id}/thread/${initiatedThreadId}`);
      } else {
        router.push(`/agents/${initiatedThreadId}`);
      }
      setInitiatedThreadId(null);
    }
  }, [threadQuery.data, initiatedThreadId, router]);

  const secondaryGradient =
    'bg-gradient-to-r from-blue-500 to-blue-500 bg-clip-text text-transparent';


  const handleSubmit = async (
    message: string,
    options?: {
      model_name?: string;
      enable_thinking?: boolean;
      reasoning_effort?: string;
      stream?: boolean;
      enable_context_manager?: boolean;
    },
  ) => {
    if (
      (!message.trim() && !chatInputRef.current?.getPendingFiles().length) ||
      isSubmitting
    )
      return;

    setIsSubmitting(true);

    try {
      const files = chatInputRef.current?.getPendingFiles() || [];
      localStorage.removeItem(PENDING_PROMPT_KEY);

      const formData = new FormData();
      formData.append('prompt', message);

      // Add selected agent if one is chosen
      if (selectedAgentId) {
        formData.append('agent_id', selectedAgentId);
      }

      files.forEach((file, index) => {
        const normalizedName = normalizeFilenameToNFC(file.name);
        formData.append('files', file, normalizedName);
      });

      if (options?.model_name) formData.append('model_name', options.model_name);
      formData.append('enable_thinking', String(options?.enable_thinking ?? false));
      formData.append('reasoning_effort', options?.reasoning_effort ?? 'low');
      formData.append('stream', String(options?.stream ?? true));
      formData.append('enable_context_manager', String(options?.enable_context_manager ?? false));

      console.log('FormData content:', Array.from(formData.entries()));

      const result = await initiateAgentMutation.mutateAsync(formData);
      console.log('Agent initiated:', result);

      if (result.thread_id) {
        setInitiatedThreadId(result.thread_id);
      } else {
        throw new Error('Agent initiation did not return a thread_id.');
      }
      chatInputRef.current?.clearPendingFiles();
    } catch (error: any) {
      console.error('Error during submission process:', error);
      if (error instanceof BillingError) {
        console.log('Handling BillingError:', error.detail);
        onOpen("paymentRequiredDialog");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      const pendingPrompt = localStorage.getItem(PENDING_PROMPT_KEY);
      const urlPrompt = searchParams.get('prompt');

      if (urlPrompt) {
        setInputValue(decodeURIComponent(urlPrompt));
        // Clean up URL parameter
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('prompt');
        router.replace(newUrl.pathname + newUrl.search, { scroll: false });
      } else if (pendingPrompt) {
        setInputValue(pendingPrompt);
        setAutoSubmit(true);
      }
    }, 200);

    return () => clearTimeout(timer);
  }, [searchParams, router]);

  useEffect(() => {
    if (autoSubmit && inputValue && !isSubmitting) {
      const timer = setTimeout(() => {
        handleSubmit(inputValue);
        setAutoSubmit(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [autoSubmit, inputValue, isSubmitting]);

  // Handler for when Examples component needs to show integration requirements
  const handleRequireIntegrations = (appKeys: string[], promptTitle: string) => {
    setFilteredAppKeys(appKeys);
    setModalTitle('Connect Required Apps');
    setModalDescription(`To use "${promptTitle}", you need to connect the following integrations:`);
    setIsFilteredModalOpen(true);
  };

  // Handle MCP server connection
  const handleConnect = async (appKey: string, appName: string) => {
    if (connectingApps.has(appKey) || connectedApps.has(appKey)) return;

    setConnectingApps(prev => new Set(prev).add(appKey));

    try {
      // Initiate connection
      const initResult = await ComposioMCPService.initiateConnection(appKey);

      // Store flags for post-OAuth handling
      localStorage.setItem('composio_recently_connected', appKey);
      localStorage.setItem('composio_connection_request_id', initResult.connection_request_id);
      localStorage.setItem('composio_connection_app_name', appName);

      // Use full page redirect for authentication
      window.location.href = initResult.redirect_url;

    } catch (error: any) {
      console.error('Connection error:', error);
    } finally {
      setConnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  };

  // Handle MCP server disconnection
  const handleDisconnect = async (appKey: string, appName: string) => {
    // Implementation for disconnect if needed
    console.log('Disconnect not implemented yet:', appKey, appName);
  };

  // Show loading state while installing template
  if (templateInstallation.isInstalling) {
    return (
      <div className="flex flex-col h-full w-full">
        <div className="flex-1 flex flex-col items-center justify-center px-4">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <h3 className="text-lg font-medium">Setting up your AI assistant...</h3>
            <p className="text-sm text-muted-foreground">This will only take a moment</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <ModalProviders />

      {/* Mobile Header */}
      {isMobile && (
        <div className="bg-background border-b border-border px-4 py-3 flex items-center justify-between md:hidden">
          <div className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-primary" />
            <h1 className="text-lg font-semibold text-foreground">Dashboard</h1>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setOpenMobile(!openMobile)}
              >
                {openMobile ? (
                  <X className="h-4 w-4" />
                ) : (
                  <Menu className="h-4 w-4" />
                )}
                <span className="sr-only">
                  {openMobile ? "Close menu" : "Open menu"}
                </span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {openMobile ? "Close menu" : "Open menu"}
            </TooltipContent>
          </Tooltip>
        </div>
      )}

      <div className="flex flex-col min-h-screen w-full">
        {/* Main Content Container */}
        <div className="flex-1 flex flex-col items-center justify-center px-4 py-8">
          {/* Welcome Text */}
          <div className="w-full max-w-4xl mx-auto px-8 mb-0">
            <div className="text-center space-y-2">
              <p className="text-lg text-muted-foreground">
                Atlas, your multi-agentic AI automation platform
              </p>
            </div>
          </div>

          {/* Chat Input Section */}
          <div className="w-full max-w-4xl mx-auto px-4 sm:px-8 mb-8">
            <div className={cn(
              "w-full",
              "max-w-full",
              "sm:max-w-full"
            )}>
              <ChatInput
                ref={chatInputRef}
                onSubmit={handleSubmit}
                loading={isSubmitting}
                placeholder="what can i help you automate today...?"
                value={inputValue}
                onChange={setInputValue}
                hideAttachments={false}
                selectedAgentId={selectedAgentId}
                onAgentSelect={setSelectedAgentId}
                showSuggestions={showSuggestions}
                onToggleSuggestions={() => setShowSuggestions(!showSuggestions)}
                enableAdvancedConfig={true}
              />


            </div>
          </div>

          {/* Suggestions Section - Full Width, Below MCP Carousel */}
          <AnimatePresence>
            {showSuggestions && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
                className="w-full max-w-3xl mx-auto overflow-hidden px-4 pb-4"
              >
                <Examples
                  onSelectPrompt={setInputValue}
                  onRequireIntegrations={handleRequireIntegrations}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Old MCP Integrations Section - Deprecated */}
          {/* <div className="w-full max-w-6xl px-8 pb-8">
            <MCPIntegrations />
          </div> */}
        </div>

        <BillingErrorAlert
          message={billingError?.message}
          currentUsage={billingError?.currentUsage}
          limit={billingError?.limit}
          accountId={personalAccount?.account_id}
          onDismiss={clearBillingError}
          isOpen={!!billingError}
        />
      </div>
      <BillingModal
        open={showBillingModal}
        onOpenChange={setShowBillingModal}
        returnUrl={typeof window !== 'undefined' ? window.location.href : '/dashboard'}
      />

      {/* Filtered MCP Apps Modal for Integration Requirements */}
      <MCPAppsModal
        isOpen={isFilteredModalOpen}
        onClose={() => setIsFilteredModalOpen(false)}
        apps={apps}
        connectedApps={connectedApps}
        connectingApps={connectingApps}
        disconnectingApps={disconnectingApps}
        onConnect={handleConnect}
        onDisconnect={handleDisconnect}
        isLoading={loading}
        filterAppKeys={filteredAppKeys}
        filterTitle={modalTitle}
        filterDescription={modalDescription}
      />

      {/* Agent Template Selector for New Users */}
      <AgentTemplateSelector
        isOpen={showAgentTemplateSelector}
        onClose={() => setShowAgentTemplateSelector(false)}
        onSelectTemplate={handleTemplateSelect}
      />
      
    </>
  );
}
