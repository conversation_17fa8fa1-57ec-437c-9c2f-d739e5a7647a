'use client';

import { useMemo, useEffect } from 'react';
import { LiquidButton } from '@/components/ui/liquid-glass-button';

interface GoogleSignInProps {
  returnUrl?: string;
}

export default function GoogleSignIn({ 
  returnUrl
}: GoogleSignInProps) {
  const googleClientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // Pre-compute the OAuth URL for instant redirect
  const oauthUrl = useMemo(() => {
    if (!supabaseUrl || !supabaseAnonKey) return null;
    
    // Always pass a returnUrl - default to dashboard
    const finalReturnUrl = returnUrl || '/dashboard';
    const redirectTo = `${window.location.origin}/auth/callback?returnUrl=${encodeURIComponent(finalReturnUrl)}`;
    const encodedRedirectTo = encodeURIComponent(redirectTo);
    
    // Direct Supabase OAuth URL - bypasses SDK overhead
    return `${supabaseUrl}/auth/v1/authorize?provider=google&redirect_to=${encodedRedirectTo}&scopes=email+profile&access_type=offline&prompt=select_account`;
  }, [supabaseUrl, supabaseAnonKey, returnUrl]);

  // Instant redirect - no async, no loading state
  const handleGoogleSignIn = () => {
    if (oauthUrl) {
      window.location.href = oauthUrl;
    }
  };

  // Preconnect to Google OAuth to reduce latency
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = 'https://accounts.google.com';
      document.head.appendChild(link);
      
      const link2 = document.createElement('link');
      link2.rel = 'dns-prefetch';
      link2.href = 'https://accounts.google.com';
      document.head.appendChild(link2);
    }
  }, []);


  if (!googleClientId) {
    return (
      <button
        disabled
        className="w-full h-12 flex items-center justify-center gap-2 text-sm font-medium tracking-wide rounded-full bg-background border border-border opacity-60 cursor-not-allowed"
      >
        <svg className="w-5 h-5" viewBox="0 0 24 24">
          <path
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            fill="#4285F4"
          />
          <path
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            fill="#34A853"
          />
          <path
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            fill="#FBBC05"
          />
          <path
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            fill="#EA4335"
          />
        </svg>
        Google Sign-In Not Configured
      </button>
    );
  }

  return (
    <LiquidButton
      onClick={handleGoogleSignIn}
      className="w-full h-16 text-base font-medium relative pr-12"
      size="xxl"
    >
      <div className="flex items-center justify-center gap-4">
        {/* Google logo */}
        <svg className="w-6 h-6" viewBox="0 0 24 24">
          <path
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            fill="#4285F4"
          />
          <path
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            fill="#34A853"
          />
          <path
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            fill="#FBBC05"
          />
          <path
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            fill="#EA4335"
          />
        </svg>

        <span>Login/Sign up with Google</span>
      </div>
    </LiquidButton>
  );
}