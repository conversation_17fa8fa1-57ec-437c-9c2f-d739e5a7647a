'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Loader2 } from 'lucide-react';
import { getMCPIconComponent, ToolCase } from '@/lib/icon-mapping';
import { cn } from '@/lib/utils';
import { usePublicStarterTemplates, PublicTemplate } from '@/hooks/react-query/use-public-templates';
import { LiquidButton } from '@/components/ui/liquid-glass-button';
import { PostHogService } from '@/lib/analytics/posthog';

interface AgentTemplateSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTemplate: (templateId: string) => void;
}


export function AgentTemplateSelector({
  isOpen,
  onClose,
  onSelectTemplate
}: AgentTemplateSelectorProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);

  // Fetch starter templates from public API
  const { data: templates, isLoading } = usePublicStarterTemplates();

  // Handle template selection
  const handleSelectTemplate = (templateId: string, templateName: string) => {
    setSelectedTemplate(templateId);
    setIsAnimating(true);

    // Track agent template selection in PostHog
    PostHogService.trackAgentTemplate('agent_template_selected', {
      template_id: templateId,
      template_name: templateName,
      selected_at: new Date().toISOString()
    });

    // Trigger callback after brief animation
    setTimeout(() => {
      onSelectTemplate(templateId);
    }, 300);
  };

  // Handle modal close tracking
  const handleClose = () => {
    // Track modal close without selection
    if (!selectedTemplate) {
      PostHogService.trackAgentTemplate('agent_template_modal_closed', {
        selection_made: false,
        closed_at: new Date().toISOString()
      });
    }
    onClose();
  };

  // Reset state when closed
  useEffect(() => {
    if (!isOpen) {
      setSelectedTemplate(null);
      setIsAnimating(false);
    }
  }, [isOpen]);

  const displayTemplates = (templates as PublicTemplate[]) || [];

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
            onClick={handleClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 flex items-center justify-center z-50 p-4"
          >
            <div className="relative w-full max-w-3xl max-h-[90vh] overflow-hidden">
              {/* Glass container */}
              <div className="relative rounded-2xl backdrop-blur-xl bg-white/10 dark:bg-black/20 border border-white/20 dark:border-white/10 shadow-2xl">
                {/* Glass effect overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5 rounded-2xl pointer-events-none" />

                {/* Content */}
                <div className="relative p-8">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="space-y-1">
                      <h2 className="text-2xl font-semibold text-white">
                        Choose Your AI Agent
                      </h2>
                      <p className="text-white/60 text-sm">
                        Select a pre-built agent template to get started
                      </p>
                    </div>
                    <button
                      onClick={handleClose}
                      className="p-2 rounded-full hover:bg-white/10 transition-colors"
                    >
                      <X className="h-5 w-5 text-white/60 hover:text-white" />
                    </button>
                  </div>

                  {/* Templates Grid */}
                  {isLoading ? (
                    <div className="flex items-center justify-center h-64">
                      <Loader2 className="h-8 w-8 animate-spin text-white/60" />
                    </div>
                  ) : displayTemplates.length === 0 ? (
                    <div className="flex items-center justify-center h-64">
                      <p className="text-white/60 text-center">
                        No templates available at the moment.<br />
                        Please try again later or skip to use the default agent.
                      </p>
                    </div>
                  ) : (
                    <div className="grid gap-4 w-full">
                      {displayTemplates.map((template) => (
                        <AgentTemplateCard
                          key={template.template_id}
                          template={template}
                          isSelected={selectedTemplate === template.template_id}
                          onSelect={() => handleSelectTemplate(template.template_id, template.name)}
                          isAnimating={isAnimating && selectedTemplate === template.template_id}
                        />
                      ))}
                    </div>
                  )}

                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

interface AgentTemplateCardProps {
  template: PublicTemplate;
  isSelected: boolean;
  onSelect: () => void;
  isAnimating: boolean;
}

function AgentTemplateCard({
  template,
  isSelected,
  onSelect,
  isAnimating
}: AgentTemplateCardProps) {

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      animate={isAnimating ? { scale: 1.05, opacity: 0.8 } : {}}
      className="relative w-full max-w-full overflow-hidden"
    >
      <LiquidButton
        onClick={onSelect}
        className={cn(
          "relative w-full max-w-full h-auto text-left transition-all duration-200",
          "text-white font-medium justify-start",
          "!bg-transparent hover:!scale-100 hover:!brightness-100",
          "!p-4 !pl-8 !pr-4 !py-4", // Even more left padding for text spacing
          "!box-border", // Ensure box-sizing is border-box
          "!rounded-lg", // Override to less circular corners
          isSelected
            ? "opacity-100"
            : "opacity-90 hover:opacity-100"
        )}
        size="sm"
      >
        <div className="relative flex flex-col w-full max-w-full">
        {/* Content */}
        <div className="w-full text-left">
          <h3 className="text-white font-medium text-base">
            {template.name}
          </h3>

          {/* MCP Requirements or Default Toolkit Icon */}
          <div className="flex items-center -space-x-1">
            {template.mcp_requirements && template.mcp_requirements.length > 0 ? (
              template.mcp_requirements.map((mcp, index) => {
                const IconComponent = getMCPIconComponent({
                  name: mcp.display_name,
                  displayName: mcp.display_name,
                  qualifiedName: mcp.qualified_name,
                  isCustom: false,
                  type: 'configured_mcp'
                });

                return (
                  <div
                    key={index}
                    title={mcp.display_name}
                    className="relative"
                    style={{ zIndex: template.mcp_requirements.length - index }}
                  >
                    {React.createElement(IconComponent, {
                      className: "w-6 h-6 text-white/70"
                    })}
                  </div>
                );
              })
            ) : (
              <div
                title="Default toolkit"
                className="relative"
              >
                <ToolCase className="w-6 h-6 text-white/70" />
              </div>
            )}
          </div>
        </div>
        </div>
      </LiquidButton>

      {/* Selection indicator */}
      {isSelected && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute right-3 top-3 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg z-50"
          style={{ zIndex: 9999 }}
        >
          <svg
            className="w-4 h-4 text-white"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="3"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path d="M5 13l4 4L19 7" />
          </svg>
        </motion.div>
      )}
    </motion.div>
  );
}
