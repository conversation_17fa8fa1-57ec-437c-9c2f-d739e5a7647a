'use client';

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { useTheme } from 'next-themes';

export function RouteThemeController() {
  const pathname = usePathname();
  const { theme, setTheme } = useTheme();
  const userThemeRef = useRef<string | undefined>();

  useEffect(() => {
    const isDashboardRoute = pathname.startsWith('/dashboard') || 
                           pathname.startsWith('/agents') ||
                           pathname.startsWith('/settings') ||
                           pathname.startsWith('/marketplace') ||
                           pathname.startsWith('/workflows') ||
                           pathname.startsWith('/projects') ||
                           pathname.startsWith('/prompts');

    if (isDashboardRoute) {
      // Restore user's preferred theme on dashboard routes
      if (userThemeRef.current) {
        setTheme(userThemeRef.current);
      }
    } else {
      // Save current theme before forcing dark mode
      if (theme && theme !== 'dark') {
        userThemeRef.current = theme;
      }
      setTheme('dark');
    }
  }, [pathname, theme, setTheme]);

  return null;
}