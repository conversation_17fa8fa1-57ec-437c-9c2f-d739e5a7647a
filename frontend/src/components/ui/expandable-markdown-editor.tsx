import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "./textarea";
import { cn } from "@/lib/utils";
import { Edit2, Expand, Save, X } from "lucide-react";
import { Markdown } from "@/components/ui/markdown";

interface ExpandableMarkdownEditorProps {
  value: string;
  onSave: (value: string) => void;
  className?: string;
  placeholder?: string;
  title?: string;
  disabled?: boolean;
}

export const ExpandableMarkdownEditor: React.FC<ExpandableMarkdownEditorProps> = ({
  value,
  onSave,
  className = '',
  placeholder = 'Click to edit...',
  title = 'Edit Instructions',
  disabled = false
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);

  useEffect(() => {
    setEditValue(value);
  }, [value]);

  const handleSave = () => {
    onSave(editValue);
    setIsEditing(false);
    setIsDialogOpen(false);
  };

  const handleCancel = () => {
    setEditValue(value);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel();
    } else if (e.key === 'Enter' && e.metaKey) {
      handleSave();
    }
  };

  const openDialog = () => {
    setIsDialogOpen(true);
    setIsEditing(true); // Auto-enter edit mode when expanded
  };

  const startEditing = () => {
    setIsEditing(true);
  };

  // Truncate content for preview
  const truncatedValue = value.length > 200 ? value.substring(0, 200) : value;
  const isTruncated = value.length > 200;

  return (
    <>
      <div className={cn('relative', className)}>
        <div
          className="group relative pb-4 border rounded-2xl bg-muted/30 hover:opacity-80 transition-colors cursor-pointer overflow-hidden"
          onClick={openDialog}
        >
          <div className="p-4 max-h-32 overflow-hidden">
            {value ? (
              <div className="prose prose-sm dark:prose-invert max-w-none text-sm">
                <Markdown>{truncatedValue}</Markdown>
              </div>
            ) : (
              <div className="text-muted-foreground italic text-sm">
                {placeholder}
              </div>
            )}
          </div>
          {isTruncated && (
            <div className="absolute bottom-2 left-4 text-xs text-muted-foreground/60 z-10">
              .........
            </div>
          )}
          <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
            <Button
              size="sm"
              className="h-6 w-6 p-0 shadow-sm"
              onClick={(e) => {
                e.stopPropagation();
                openDialog();
              }}
            >
              <Expand className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] w-[95vw] md:w-full flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{title}</span>
              {!isEditing && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={startEditing}
                  className="h-7 mr-4"
                >
                  <Edit2 className="h-3 w-3" />
                  Edit
                </Button>
              )}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            {isEditing ? (
              <div className="h-full flex flex-col gap-2">
                <ScrollArea className="flex-1 h-[500px]">
                  <Textarea
                    value={editValue}
                    onChange={(e) => setEditValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    className="w-full h-[500px] rounded-xl bg-muted/30 p-4 resize-none overflow-y-auto"
                    style={{ minHeight: '300px' }}
                    disabled={disabled}
                    placeholder="Define the agent's role, behavior, and expertise..."
                  />
                </ScrollArea>
                <div className="text-xs text-muted-foreground/50 flex-shrink-0">
                  Markdown supported • Cmd+Enter to save • Esc to cancel
                </div>
              </div>
            ) : (
              <ScrollArea className="flex-1 h-[500px]">
                <div className="pr-4">
                  {value ? (
                    <div className="prose prose-sm dark:prose-invert max-w-none">
                      <Markdown>{value}</Markdown>
                    </div>
                  ) : (
                    <div className="text-muted-foreground italic text-center py-8">
                      {placeholder}
                    </div>
                  )}
                </div>
              </ScrollArea>
            )}
          </div>

          {isEditing && (
            <DialogFooter>
              <Button
                size="sm"
                variant="outline"
                onClick={handleCancel}
                className="h-8"
              >
                <X className="h-3 w-3" />
                Cancel
              </Button>
              <Button
                size="sm"
                variant="default"
                onClick={handleSave}
                className="h-8"
              >
                <Save className="h-3 w-3" />
                Save
              </Button>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};