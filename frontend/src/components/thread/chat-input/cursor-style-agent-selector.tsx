'use client';

import React, { useState, useEffect } from 'react';
import { ChevronDown, Plus, Infinity, Bot, Check, Search, Command } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useAgents } from '@/hooks/react-query/agents/use-agents';
import { useRouter } from 'next/navigation';
import { getAgentMCPTools } from '@/lib/icon-mapping';


interface CursorStyleAgentSelectorProps {
  selectedAgentId?: string;
  onAgentSelect: (agentId: string | undefined) => void;
  disabled?: boolean;
  className?: string;
}



// Icon tray component for MCP tools (exactly like suggestions component)
const MCPIconTray = ({ tools, maxIcons = 3 }: { tools: Array<{name: string, IconComponent: React.ComponentType<any>}>, maxIcons?: number }) => {
  if (!tools || tools.length === 0) return null;

  const visibleTools = tools.slice(0, maxIcons);
  const remainingCount = tools.length - maxIcons;

  return (
    <div className="flex items-center">
      {visibleTools.map((tool, idx) => {
        const IconComponent = tool.IconComponent;
        return (
          <div
            key={tool.name}
            className="relative"
            style={{
              height: 20,
              width: 20,
              marginLeft: idx > 0 ? '-6px' : '0',
              zIndex: visibleTools.length - idx,
            }}
          >
            <IconComponent className="w-full h-full" />
          </div>
        );
      })}
      {remainingCount > 0 && (
        <div
          className="flex items-center justify-center bg-muted border border-border rounded-full text-xs text-muted-foreground font-medium shadow-sm"
          style={{
            height: 24,
            width: 24,
            marginLeft: '-8px',
            zIndex: 0,
          }}
        >
          +{remainingCount}
        </div>
      )}
    </div>
  );
};

export function CursorStyleAgentSelector({
  selectedAgentId,
  onAgentSelect,
  disabled = false,
  className,
}: CursorStyleAgentSelectorProps) {
  const [open, setOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();

  const { data: agentsResponse, isLoading } = useAgents({
    limit: 100,
    sort_by: 'name',
    sort_order: 'asc'
  });

  const agents = agentsResponse?.agents || [];
  const selectedAgent = agents?.find(a => a.agent_id === selectedAgentId);
  const defaultAgent = agents?.find(a => a.is_default);
  const isUsingSuna = !selectedAgent && !defaultAgent;

  // Filter agents based on search query
  const filteredAgents = agents.filter(agent => 
    agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    agent.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Display the currently selected agent's name, or default agent's name, or fallback to "Atlas"
  const displayName = selectedAgent?.name || defaultAgent?.name || 'Atlas';

  // Auto-select default agent when available and no agent is currently selected
  useEffect(() => {
    if (!selectedAgentId && defaultAgent && !isLoading && onAgentSelect) {
      onAgentSelect(defaultAgent.agent_id);
    }
  }, [selectedAgentId, defaultAgent, isLoading, onAgentSelect]);

  // Keyboard shortcut for opening agent selector (Cmd+K / Ctrl+K)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault();
        setOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (open) {
      // Small delay to ensure the dropdown is rendered
      setTimeout(() => {
        const searchInput = document.querySelector('[data-agent-search-input]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }, 100);
    } else {
      // Clear search when dropdown closes
      setSearchQuery('');
    }
  }, [open]);

  const handleCreateAgent = () => {
    setOpen(false);
    router.push('/agents');
  };

  const handleClearSelection = () => {
    onAgentSelect(undefined);
    setOpen(false);
  };

  return (
    <div className="flex items-center gap-1">
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              'flex items-center gap-1.5 text-foreground bg-muted/50 hover:text-foreground transition-all duration-200 px-2.5 py-1.5 rounded-md hover:bg-muted/50 border border-border/30 hover:border-border/30 flex-shrink-0',
              disabled && 'opacity-50 cursor-not-allowed',
              className
            )}
            disabled={disabled}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {/* Button Content */}
            <div className="flex items-center gap-1">
              <Infinity className="h-3 w-3" />
              <span className="text-xs select-none truncate max-w-[80px]">
                {displayName}
              </span>
              <ChevronDown className="h-3 w-3" />
            </div>
          </Button>
        </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="w-80 sm:w-80 max-w-[calc(100vw-2rem)] p-0 border border-border/20 shadow-lg"
        sideOffset={8}
      >
        <div className="p-3 sm:p-3 border-b border-border/20">
          <div className="flex items-center gap-2 px-3 py-3 sm:py-2 bg-muted/50 rounded-md">
            <Search className="h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search agents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Escape') {
                  setOpen(false);
                  setSearchQuery('');
                }
              }}
              className="flex-1 bg-transparent border-none outline-none text-sm placeholder:text-muted-foreground"
              data-agent-search-input
            />
            <kbd className="hidden md:inline-flex h-5 select-none items-center gap-1 rounded border border-border/30 bg-muted px-1.5 font-mono text-xs text-muted-foreground">
              <Command className="h-3 w-3" />
              K
            </kbd>
          </div>
        </div>

        <div className="max-h-64 sm:max-h-64 max-h-[50vh] overflow-y-auto p-2">
          {/* Default agent option */}
          {(searchQuery === '' || 
            (defaultAgent && (defaultAgent.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
             defaultAgent.description?.toLowerCase().includes(searchQuery.toLowerCase())))) && (
            defaultAgent ? (
              <DropdownMenuItem
                className="flex items-center gap-3 px-3 py-4 sm:py-3 text-sm rounded-lg cursor-pointer group hover:bg-accent/50 transition-colors"
                onSelect={() => {
                  onAgentSelect(defaultAgent.agent_id);
                  setOpen(false);
                  setSearchQuery('');
                }}
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary">
                  <Bot className="h-4 w-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-foreground">{defaultAgent.name}</span>
                    <div className="px-2 py-0.5 text-xs bg-primary/10 text-primary rounded-full">Default</div>
                    <MCPIconTray tools={getAgentMCPTools(defaultAgent)} maxIcons={3} />
                  </div>
                  <p className="text-xs text-muted-foreground line-clamp-1">
                    {defaultAgent.description || 'Your personal AI employee'}
                  </p>
                </div>
                {selectedAgentId === defaultAgent.agent_id && (
                  <Check className="h-4 w-4 text-primary" />
                )}
              </DropdownMenuItem>
            ) : (
              <DropdownMenuItem
                className="flex items-center gap-3 px-3 py-4 sm:py-3 text-sm rounded-lg cursor-pointer group hover:bg-accent/50 transition-colors"
                onSelect={() => {
                  handleClearSelection();
                  setSearchQuery('');
                }}
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary">
                  <Bot className="h-4 w-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-foreground">Atlas</span>
                    <div className="px-2 py-0.5 text-xs bg-primary/10 text-primary rounded-full">Default</div>
                  </div>
                  <p className="text-xs text-muted-foreground line-clamp-1">
                    Your personal AI employee
                  </p>
                </div>
                {isUsingSuna && (
                  <Check className="h-4 w-4 text-primary" />
                )}
              </DropdownMenuItem>
            )
          )}

          {/* Custom agents */}
          {filteredAgents.filter(agent => !agent.is_default).map((agent) => (
            <DropdownMenuItem
              key={agent.agent_id}
              className="flex items-center gap-3 px-3 py-4 sm:py-3 text-sm rounded-lg cursor-pointer group hover:bg-accent/50 transition-colors"
              onSelect={() => {
                onAgentSelect(agent.agent_id);
                setOpen(false);
                setSearchQuery('');
              }}
            >
              <div className="flex items-center justify-center w-10 h-10 sm:w-8 sm:h-8 rounded-full bg-secondary/10 text-secondary-foreground">
                {agent.avatar ? (
                  <span className="text-base sm:text-sm">{agent.avatar}</span>
                ) : (
                  <Bot className="h-5 w-5 sm:h-4 sm:w-4" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-foreground">{agent.name}</span>
                  <div className="px-2 py-0.5 text-xs bg-secondary/10 text-secondary-foreground rounded-full">Custom</div>
                  <MCPIconTray tools={getAgentMCPTools(agent)} maxIcons={3} />
                </div>
                {agent.description && (
                  <p className="text-xs text-muted-foreground line-clamp-1">
                    {agent.description}
                  </p>
                )}
              </div>
              {selectedAgentId === agent.agent_id && (
                <Check className="h-4 w-4 text-primary" />
              )}
            </DropdownMenuItem>
          ))}

          {/* No results found */}
          {searchQuery && filteredAgents.length === 0 && !defaultAgent?.name.toLowerCase().includes(searchQuery.toLowerCase()) && (
            <div className="px-3 py-6 text-center text-sm text-muted-foreground">
              No agents found for "{searchQuery}"
            </div>
          )}

          {/* Create new agent option */}
          <DropdownMenuSeparator className="my-2" />
          <DropdownMenuItem
            className="flex items-center gap-3 px-3 py-3 text-sm rounded-lg cursor-pointer text-muted-foreground hover:text-foreground hover:bg-accent/50 transition-colors group"
            onSelect={() => {
              handleCreateAgent();
              setSearchQuery('');
            }}
          >
            <div className="flex items-center justify-center w-10 h-10 sm:w-8 sm:h-8 rounded-full bg-muted/50">
              <Plus className="h-5 w-5 sm:h-4 sm:w-4" />
            </div>
            <div className="flex-1">
              <span className="text-sm font-medium">Create new agent</span>
              <p className="text-xs text-muted-foreground">Build a custom AI agent</p>
            </div>
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
  );
}
