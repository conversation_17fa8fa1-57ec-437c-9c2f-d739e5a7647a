'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ComposioApp } from '@/types/composio';
import { ComposioMCPService } from '@/lib/composio-api';
import { getComposioAppIcon } from '@/lib/icon-mapping';
import { MCPAppsModal } from '@/components/dashboard/mcp-apps-modal';
import { MCPServerCarousel } from '@/components/dashboard/mcp-server-carousel';
import { createClient } from '@/lib/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import { agentKeys } from '@/hooks/react-query/agents/keys';


import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';

interface ChatInputPocketProps {
  className?: string;
  showSuggestions?: boolean;
  onToggleSuggestions?: () => void;
}

export function ChatInputPocket({ className, showSuggestions = true, onToggleSuggestions }: ChatInputPocketProps) {
  const queryClient = useQueryClient();
  const isMobile = useIsMobile();
  const [apps, setApps] = useState<ComposioApp[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // MCP connection states
  const [connectingApps, setConnectingApps] = useState<Set<string>>(new Set());
  const [disconnectingApps, setDisconnectingApps] = useState<Set<string>>(new Set());
  const [connectedApps, setConnectedApps] = useState<Set<string>>(new Set());

  // Load apps and connections
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Load supported apps
        const appsData = await ComposioMCPService.getSupportedApps();
        if (appsData.success) {
          setApps(appsData.apps);
        }

        // Load connections
        const connections = await ComposioMCPService.listUserConnections();
        const connectedAppKeys = new Set(
          connections.map((conn: any) => conn.app_key as string)
        );
        setConnectedApps(connectedAppKeys);
      } catch (error) {
        console.error('Error loading pocket data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Helper function to get authenticated headers
  const getAuthHeaders = async () => {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error('No authentication token available. Please sign in.');
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    };
  };

  // Handle MCP server connection
  const handleConnect = async (appKey: string, appName: string) => {
    if (connectingApps.has(appKey) || connectedApps.has(appKey)) return;

    setConnectingApps(prev => new Set(prev).add(appKey));
    toast("Authenticating...");

    try {
      const initResult = await ComposioMCPService.initiateConnection(appKey);

      localStorage.setItem('composio_recently_connected', appKey);
      localStorage.setItem('composio_connection_request_id', initResult.connection_request_id);
      localStorage.setItem('composio_connection_app_name', appName);

      window.location.href = initResult.redirect_url;
    } catch (error: any) {
      console.error('Connection error:', error);
    } finally {
      setConnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  };

  // Handle MCP server disconnection
  const handleDisconnect = async (appKey: string, appName: string) => {
    if (disconnectingApps.has(appKey) || !connectedApps.has(appKey)) return;

    setDisconnectingApps(prev => new Set(prev).add(appKey));

    try {
      setConnectedApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });

      const success = await ComposioMCPService.deleteConnection(appKey);

      if (success) {
        queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
        queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });
      } else {
        setConnectedApps(prev => new Set(prev).add(appKey));
      }
    } catch (error: any) {
      console.error('Disconnect error:', error);
      setConnectedApps(prev => new Set(prev).add(appKey));
    } finally {
      setDisconnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  };

  // Don't render if no apps or still loading
  if (loading || apps.length === 0) {
    return null;
  }

  // On mobile, render the MCP Server Carousel instead
  if (isMobile) {
    return (
      <>
        <div className={`w-full ${className}`}>
          <div className="flex flex-col gap-3">
            {/* MCP Server Carousel for mobile - Edge to edge */}
            <div className="relative -mx-4 sm:-mx-8">
              <MCPServerCarousel className="px-4" />
            </div>


          </div>
        </div>

        {/* MCP Apps Modal */}
        <MCPAppsModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          apps={apps}
          connectedApps={connectedApps}
          connectingApps={connectingApps}
          disconnectingApps={disconnectingApps}
          onConnect={handleConnect}
          onDisconnect={handleDisconnect}
          isLoading={loading}
        />
      </>
    );
  }

  // Desktop version - keep the existing carousel
  const duplicatedApps = [...apps, ...apps];

  return (
    <>
      <div className={`w-full max-w-2xl mx-auto ${className}`}>
        <div className="flex items-center gap-4">
          {/* Carousel container */}
          <div
            className="relative h-12 overflow-hidden cursor-pointer rounded-lg bg-muted/20 border border-border/30 hover:bg-muted/30 transition-colors flex-1"
            onClick={() => setIsModalOpen(true)}
          >
          {/* Moving carousel container */}
          <motion.div
            className="flex items-center h-full"
            animate={{
              x: [0, -apps.length * 48], // Move by the width of original apps array
            }}
            transition={{
              duration: apps.length * 2, // Adjust speed based on number of apps
              ease: "linear",
              repeat: Infinity,
            }}
          >
            {duplicatedApps.map((app, index) => {
              const IconComponent = getComposioAppIcon(app);
              const isConnected = connectedApps.has(app.key);

              return (
                <div
                  key={`${app.key}-${index}`}
                  className="flex-shrink-0 w-12 h-12 flex items-center justify-center"
                >
                  <div className="relative">
                    {/* App Icon */}
                    <div className="w-8 h-8 flex items-center justify-center">
                      <IconComponent className="w-6 h-6" />
                    </div>

                    {/* Connected indicator */}
                    {isConnected && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
                    )}
                  </div>
                </div>
              );
            })}
          </motion.div>

            {/* Gradient overlays for smooth edges */}
            <div className="absolute inset-y-0 left-0 w-8 bg-gradient-to-r from-muted/20 to-transparent pointer-events-none" />
            <div className="absolute inset-y-0 right-0 w-8 bg-gradient-to-l from-muted/20 to-transparent pointer-events-none" />
          </div>
        </div>
      </div>

      {/* MCP Apps Modal */}
      <MCPAppsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        apps={apps}
        connectedApps={connectedApps}
        connectingApps={connectingApps}
        disconnectingApps={disconnectingApps}
        onConnect={handleConnect}
        onDisconnect={handleDisconnect}
        isLoading={loading}
      />
    </>
  );
}
