'use client';

import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Brain, Database, Lightbulb, ChevronDown } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { handleFiles } from './file-upload-handler';
import { MessageInput } from './message-input';
import { CursorStyleAgentSelector } from './cursor-style-agent-selector';
import { AttachmentGroup } from '../attachment-group';
import { ModelToggle } from './model-toggle';
import { useModelSelection } from './_use-model-selection';
import { useRunValidation } from '@/hooks/use-run-validation';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { MCPAppsModal } from '@/components/dashboard/mcp-apps-modal';
import { ComposioMCPService } from '@/lib/composio-api';
import { ComposioApp } from '@/types/composio';
import { getComposioAppIcon } from '@/lib/icon-mapping';
import { AgentConfigModal } from '@/components/agents/agent-config-modal';
import { motion } from 'framer-motion';

import { useFileDelete } from '@/hooks/react-query/files';
import { useQueryClient } from '@tanstack/react-query';
import { useAgent, useDefaultAgentMCPs, useUpdateAgentCustomMCPs } from '@/hooks/react-query/agents/use-agents';
import { FloatingToolPreview, ToolCallInput } from './floating-tool-preview';
import { PostHogService } from '@/lib/analytics/posthog';

export interface ChatInputHandles {
  getPendingFiles: () => File[];
  clearPendingFiles: () => void;
}

export interface ChatInputProps {
  onSubmit: (
    message: string,
    options?: { model_name?: string; enable_thinking?: boolean },
  ) => void;
  placeholder?: string;
  loading?: boolean;
  disabled?: boolean;
  isAgentRunning?: boolean;
  onStopAgent?: () => void;
  autoFocus?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  onFileBrowse?: () => void;
  sandboxId?: string;
  hideAttachments?: boolean;
  selectedAgentId?: string;
  onAgentSelect?: (agentId: string | undefined) => void;
  agentName?: string;
  messages?: any[];
  bgColor?: string;
  toolCalls?: ToolCallInput[];
  toolCallIndex?: number;
  showToolPreview?: boolean;
  onExpandToolPreview?: () => void;
  showSuggestions?: boolean;
  onToggleSuggestions?: () => void;
  enableAdvancedConfig?: boolean;
  onConfigureAgent?: (agentId: string) => void;
  hideAgentSelection?: boolean;
  hideAgentPocket?: boolean;
}

export interface UploadedFile {
  name: string;
  path: string;
  size: number;
  type: string;
  localUrl?: string;
}

export const ChatInput = forwardRef<ChatInputHandles, ChatInputProps>(
  (
    {
      onSubmit,
      placeholder = 'Describe what you need help with...',
      loading = false,
      disabled = false,
      isAgentRunning = false,
      onStopAgent,
      autoFocus = true,
      value: controlledValue,
      onChange: controlledOnChange,
      onFileBrowse,
      sandboxId,
      hideAttachments = false,
      selectedAgentId,
      onAgentSelect,
      agentName,
      messages = [],
      bgColor = 'bg-card',
      toolCalls = [],
      toolCallIndex = 0,
      showToolPreview = false,
      onExpandToolPreview,
      showSuggestions,
      onToggleSuggestions,
      enableAdvancedConfig = false,
      onConfigureAgent,
      hideAgentSelection = false,
      hideAgentPocket = false,
    },
    ref,
  ) => {
    const isMobile = useIsMobile();
    const isControlled =
      controlledValue !== undefined && controlledOnChange !== undefined;

    const [uncontrolledValue, setUncontrolledValue] = useState('');
    const value = isControlled ? controlledValue : uncontrolledValue;

    const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
    const [pendingFiles, setPendingFiles] = useState<File[]>([]);
    const [isUploading, setIsUploading] = useState(false);
    const [isDraggingOver, setIsDraggingOver] = useState(false);

    // Agent pocket state
    const [configModalOpen, setConfigModalOpen] = useState(false);
    const [configModalTab, setConfigModalTab] = useState('integrations');
    const [integrationsModalOpen, setIntegrationsModalOpen] = useState(false);

    // MCP apps state for integrations
    const [apps, setApps] = useState<ComposioApp[]>([]);
    const [connectedApps, setConnectedApps] = useState<Set<string>>(new Set());
    const [connectingApps, setConnectingApps] = useState<Set<string>>(new Set());
    const [disconnectingApps, setDisconnectingApps] = useState<Set<string>>(new Set());
    const [appsLoading, setAppsLoading] = useState(true);

    // Get selected agent data to show its integrations
    const { data: selectedAgent } = useAgent(selectedAgentId || '');
    const { data: defaultAgentMCPs } = useDefaultAgentMCPs();
    const updateAgentCustomMCPsMutation = useUpdateAgentCustomMCPs();

    const {
      selectedModel,
      setSelectedModel: handleModelChange,
      subscriptionStatus,

      canAccessModel,
      getActualModelId,

    } = useModelSelection();

    const { canSubmit, reason } = useRunValidation();

    const deleteFileMutation = useFileDelete();
    const queryClient = useQueryClient();

    const textareaRef = useRef<HTMLTextAreaElement | null>(null);
    const fileInputRef = useRef<HTMLInputElement | null>(null);

    useImperativeHandle(ref, () => ({
      getPendingFiles: () => pendingFiles,
      clearPendingFiles: () => setPendingFiles([]),
    }));

    // Load MCP apps data when pocket is enabled
    useEffect(() => {
      if (enableAdvancedConfig && selectedAgentId) {
        const loadAppsData = async () => {
          try {
            setAppsLoading(true);
            const appsData = await ComposioMCPService.getSupportedApps();
            if (appsData.success) {
              setApps(appsData.apps);
            }

            const connections = await ComposioMCPService.listUserConnections();
            const connectedAppKeys = new Set(
              connections.map((conn: any) => conn.app_key as string)
            );
            setConnectedApps(connectedAppKeys);
          } catch (error) {
            console.error('Error loading MCP apps:', error);
          } finally {
            setAppsLoading(false);
          }
        };

        loadAppsData();
      }
    }, [enableAdvancedConfig, selectedAgentId]);

    useEffect(() => {
      if (autoFocus && textareaRef.current) {
        textareaRef.current.focus();
      }
    }, [autoFocus]);

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      if (
        (!value.trim() && uploadedFiles.length === 0) ||
        loading ||
        (disabled && !isAgentRunning)
      )
        return;

      if (isAgentRunning && onStopAgent) {
        onStopAgent();
        return;
      }

      // Check if user has runs remaining before submitting
      if (!canSubmit) {
        toast.error('No runs remaining', {
          description: reason || 'You have used all your available runs. Please upgrade to continue.',
          duration: 4000,
        });
        return;
      }

      // Keep original message with tool mentions for backend processing
      // The backend now handles tool mention parsing and fetching tool information
      let message = value;

      if (uploadedFiles.length > 0) {
        const fileInfo = uploadedFiles
          .map((file) => `[Uploaded File: ${file.path}]`)
          .join('\n');
        message = message ? `${message}\n\n${fileInfo}` : fileInfo;
      }

      let baseModelName = getActualModelId(selectedModel);
      let thinkingEnabled = false;
      if (selectedModel.endsWith('-thinking')) {
        baseModelName = getActualModelId(selectedModel.replace(/-thinking$/, ''));
        thinkingEnabled = true;
      }

      // Track user message submission in PostHog
      PostHogService.trackUserMessage({
        message_length: value.trim().length,
        has_files: uploadedFiles.length > 0,
        file_count: uploadedFiles.length,
        model_name: baseModelName,
        thinking_enabled: thinkingEnabled,
        agent_id: selectedAgentId,
        agent_name: agentName,
      });

      onSubmit(message, {
        model_name: baseModelName,
        enable_thinking: thinkingEnabled,
      });

      if (!isControlled) {
        setUncontrolledValue('');
      }

      setUploadedFiles([]);
    };

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      if (isControlled) {
        controlledOnChange(newValue);
      } else {
        setUncontrolledValue(newValue);
      }
    };

    const handleTranscription = (transcribedText: string) => {
      const currentValue = isControlled ? controlledValue : uncontrolledValue;
      const newValue = currentValue ? `${currentValue} ${transcribedText}` : transcribedText;

      if (isControlled) {
        controlledOnChange(newValue);
      } else {
        setUncontrolledValue(newValue);
      }
    };

    const removeUploadedFile = (index: number) => {
      const fileToRemove = uploadedFiles[index];

      // Clean up local URL if it exists
      if (fileToRemove.localUrl) {
        URL.revokeObjectURL(fileToRemove.localUrl);
      }

      // Remove from local state immediately for responsive UI
      setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
      if (!sandboxId && pendingFiles.length > index) {
        setPendingFiles((prev) => prev.filter((_, i) => i !== index));
      }

      // Check if file is referenced in existing chat messages before deleting from server
      const isFileUsedInChat = messages.some(message => {
        const content = typeof message.content === 'string' ? message.content : '';
        return content.includes(`[Uploaded File: ${fileToRemove.path}]`);
      });

      // Only delete from server if file is not referenced in chat history
      if (sandboxId && fileToRemove.path && !isFileUsedInChat) {
        deleteFileMutation.mutate({
          sandboxId,
          filePath: fileToRemove.path,
        }, {
          onError: (error) => {
            console.error('Failed to delete file from server:', error);
          }
        });
      } else if (isFileUsedInChat) {
        console.log(`Skipping server deletion for ${fileToRemove.path} - file is referenced in chat history`);
      }
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDraggingOver(true);
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDraggingOver(false);
    };

    // MCP connection handlers
    const handleMCPConnect = async (appKey: string, appName: string) => {
      if (connectingApps.has(appKey) || connectedApps.has(appKey)) return;

      setConnectingApps(prev => new Set(prev).add(appKey));
      toast("Authenticating...");

      try {
        const initResult = await ComposioMCPService.initiateConnection(appKey);
        localStorage.setItem('composio_recently_connected', appKey);
        localStorage.setItem('composio_connection_request_id', initResult.connection_request_id);
        localStorage.setItem('composio_connection_app_name', appName);
        window.location.href = initResult.redirect_url;
      } catch (error: any) {
        console.error('Connection error:', error);
        toast.error('Failed to connect to ' + appName);
      } finally {
        setConnectingApps(prev => {
          const newSet = new Set(prev);
          newSet.delete(appKey);
          return newSet;
        });
      }
    };

    const handleMCPDisconnect = async (appKey: string, appName: string) => {
      if (disconnectingApps.has(appKey) || !connectedApps.has(appKey)) return;

      setDisconnectingApps(prev => new Set(prev).add(appKey));

      try {
        setConnectedApps(prev => {
          const newSet = new Set(prev);
          newSet.delete(appKey);
          return newSet;
        });

        const success = await ComposioMCPService.deleteConnection(appKey);
        if (!success) {
          setConnectedApps(prev => new Set(prev).add(appKey));
          toast.error('Failed to disconnect from ' + appName);
        } else {
          toast.success('Disconnected from ' + appName);
        }
      } catch (error: any) {
        console.error('Disconnect error:', error);
        setConnectedApps(prev => new Set(prev).add(appKey));
        toast.error('Failed to disconnect from ' + appName);
      } finally {
        setDisconnectingApps(prev => {
          const newSet = new Set(prev);
          newSet.delete(appKey);
          return newSet;
        });
      }
    };

    // Agent-specific MCP logic
    const agentConnectedApps = React.useMemo(() => {
      if (!selectedAgent?.custom_mcps) {
        return new Set<string>();
      }

      const composioMCPs = selectedAgent.custom_mcps.filter(mcp =>
        mcp.config?.url?.includes('mcp.composio.dev') || mcp.auth_type === 'composio_v3'
      );

      // Map all possible identifiers from MCP configuration
      const appIdentifiers = new Set<string>();
      composioMCPs.forEach(mcp => {
        // Add name and app_name
        if (mcp.name) appIdentifiers.add(mcp.name);
        if (mcp.app_name) appIdentifiers.add(mcp.app_name);

        // Extract app identifier from URL if present
        if (mcp.config?.url) {
          // Match patterns like: /composio/server/{server_id}/mcp
          const serverMatch = mcp.config.url.match(/\/composio\/server\/([^\/]+)\/mcp/);
          if (serverMatch) appIdentifiers.add(serverMatch[1]);

          // Also try simpler pattern: /composio/{app_name}
          const simpleMatch = mcp.config.url.match(/\/composio\/([^\/\?]+)/);
          if (simpleMatch) appIdentifiers.add(simpleMatch[1]);
        }

        // Add any other potential identifier fields from config
        if ((mcp as any).app_key) appIdentifiers.add((mcp as any).app_key);
        if ((mcp as any).integration_name) appIdentifiers.add((mcp as any).integration_name);
      });


      return appIdentifiers;
    }, [selectedAgent]);

    const defaultAgentConnectedApps = React.useMemo(() => {
      if (!defaultAgentMCPs?.custom_mcps) {
        return new Set<string>();
      }

      const composioMCPs = defaultAgentMCPs.custom_mcps.filter(mcp =>
        mcp.config?.url?.includes('mcp.composio.dev') || mcp.auth_type === 'composio_v3'
      );

      // Map all possible identifiers from MCP configuration
      const appIdentifiers = new Set<string>();
      composioMCPs.forEach(mcp => {
        // Add name and app_name
        if (mcp.name) appIdentifiers.add(mcp.name);
        if (mcp.app_name) appIdentifiers.add(mcp.app_name);

        // Extract app identifier from URL if present
        if (mcp.config?.url) {
          // Match patterns like: /composio/server/{server_id}/mcp
          const serverMatch = mcp.config.url.match(/\/composio\/server\/([^\/]+)\/mcp/);
          if (serverMatch) appIdentifiers.add(serverMatch[1]);

          // Also try simpler pattern: /composio/{app_name}
          const simpleMatch = mcp.config.url.match(/\/composio\/([^\/\?]+)/);
          if (simpleMatch) appIdentifiers.add(simpleMatch[1]);
        }

        // Add any other potential identifier fields from config
        if ((mcp as any).app_key) appIdentifiers.add((mcp as any).app_key);
        if ((mcp as any).integration_name) appIdentifiers.add((mcp as any).integration_name);
      });


      return appIdentifiers;
    }, [defaultAgentMCPs]);

    const handleToggleAgentApp = async (appKey: string, appName: string, enabled: boolean) => {
      if (!selectedAgentId || !selectedAgent) return;

      try {
        const currentCustomMCPs = selectedAgent.custom_mcps || [];

        if (enabled) {
          // Add app to current agent from default agent
          const defaultMCP = defaultAgentMCPs?.custom_mcps?.find(
            mcp => {
              const mcpName = mcp.name || mcp.app_name || '';
              return mcpName === appKey || mcpName === appName;
            }
          );

          if (defaultMCP) {
            const newCustomMCPs = [...currentCustomMCPs, defaultMCP];
            await updateAgentCustomMCPsMutation.mutateAsync({
              agentId: selectedAgentId,
              customMcps: newCustomMCPs
            });
            toast.success(`Added ${appName} to agent`);
          }
        } else {
          // Remove app from current agent
          const updatedCustomMCPs = currentCustomMCPs.filter(
            mcp => {
              const mcpName = mcp.name || mcp.app_name || '';
              return mcpName !== appKey && mcpName !== appName;
            }
          );
          await updateAgentCustomMCPsMutation.mutateAsync({
            agentId: selectedAgentId,
            customMcps: updatedCustomMCPs
          });
          toast.success(`Removed ${appName} from agent`);
        }
      } catch (error) {
        console.error('Error toggling agent app:', error);
        toast.error(`Failed to ${enabled ? 'add' : 'remove'} ${appName}`);
      }
    };

    return (
      <div className="mx-auto w-full max-w-3xl">
        {/* Floating Tool Preview */}
        {showToolPreview && (
          <FloatingToolPreview
            key={`tool-preview-${toolCalls.length}-${toolCallIndex}`}
            toolCalls={toolCalls}
            currentIndex={toolCallIndex}
            onExpand={onExpandToolPreview || (() => {})}
            agentName={agentName}
            isVisible={showToolPreview}
          />
        )}

        <Card
          className={cn(
            "shadow-none w-full max-w-4xl mx-auto bg-transparent border-none overflow-hidden rounded-t-xl"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsDraggingOver(false);
            if (fileInputRef.current && e.dataTransfer.files.length > 0) {
              const files = Array.from(e.dataTransfer.files);
              handleFiles(
                files,
                sandboxId,
                setPendingFiles,
                setUploadedFiles,
                setIsUploading,
                messages,
                queryClient,
              );
            }
          }}
        >
          <div className={cn(
            "w-full text-sm flex flex-col justify-between items-start rounded-lg",
            isMobile && "text-xs"
          )}>
            <CardContent className={cn(
              `w-full p-1.5 pb-1 ${bgColor} border rounded-t-3xl`
            )}>
              <AttachmentGroup
                files={uploadedFiles || []}
                sandboxId={sandboxId}
                onRemove={removeUploadedFile}
                layout="inline"
                maxHeight="216px"
                showPreviews={true}
              />
              <MessageInput
                ref={textareaRef}
                value={value}
                onChange={handleChange}
                onSubmit={handleSubmit}
                onTranscription={handleTranscription}
                placeholder={placeholder}
                loading={loading}
                disabled={disabled}
                isAgentRunning={isAgentRunning}
                onStopAgent={onStopAgent}
                isDraggingOver={isDraggingOver}
                uploadedFiles={uploadedFiles}

                fileInputRef={fileInputRef}
                isUploading={isUploading}
                sandboxId={sandboxId}
                setPendingFiles={setPendingFiles}
                setUploadedFiles={setUploadedFiles}
                setIsUploading={setIsUploading}
                hideAttachments={hideAttachments}
                messages={messages}

                selectedAgentId={selectedAgentId}
                onAgentSelect={onAgentSelect}
                hideAgentSelection={hideAgentSelection}
              />
            </CardContent>

            {/* Agent Pocket Configuration Panel */}
            {!hideAgentPocket && (
              <div className="w-full border-t bg-muted/20 px-2 sm:px-4 py-2 sm:py-1.5 rounded-b-3xl border-l border-r border-b border-border/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto scrollbar-none flex-1">

                    {/* Integrations Button */}
                    <button
                      onClick={() => setIntegrationsModalOpen(true)}
                      className="flex items-center gap-1 sm:gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2 sm:px-2.5 py-2 sm:py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0 min-h-[44px] sm:min-h-auto"
                    >
                      {(() => {
                        // Get connected integrations for the current selected agent
                        const agentCustomMCPs = selectedAgent?.custom_mcps || [];
                        const composioMCPs = agentCustomMCPs.filter(mcp =>
                          mcp.config?.url?.includes('mcp.composio.dev') || mcp.auth_type === 'composio_v3'
                        );
                        const appsToShow = composioMCPs.slice(0, 3);

                        if (composioMCPs.length === 0) {
                          // Show greyed out GSuite icons when no apps connected
                          const gsuiteApps = [
                            { key: 'gmail', name: 'Gmail' },
                            { key: 'googledocs', name: 'Google Docs' },
                            { key: 'googlesheets', name: 'Google Sheets' }
                          ];
                          
                          return (
                            <>
                              <div className="flex items-center -space-x-1">
                                {gsuiteApps.map((app, index) => {
                                  const IconComponent = getComposioAppIcon(app);
                                  return (
                                    <div
                                      key={index}
                                      className="w-4 h-4 rounded-lg overflow-hidden flex items-center justify-center bg-muted/50 opacity-30"
                                    >
                                      <IconComponent className="w-3.5 h-3.5" />
                                    </div>
                                  );
                                })}
                              </div>
                              <span className="text-xs sm:text-xs font-medium hidden sm:inline">Integrations</span>
                            </>
                          );
                        }

                        return (
                          <>
                            <div className="flex items-center -space-x-1">
                              {appsToShow.map((mcp, index) => {
                                // Create a mock app object for the icon function
                                const mockApp = {
                                  key: mcp.name || mcp.app_name || 'unknown',
                                  name: mcp.name || mcp.app_name || 'Unknown',
                                  icon: ''
                                };
                                const IconComponent = getComposioAppIcon(mockApp);
                                return (
                                  <div
                                    key={index}
                                    className="w-4 h-4 rounded-lg overflow-hidden flex items-center justify-center bg-muted/50"
                                  >
                                    <IconComponent className="w-3.5 h-3.5" />
                                  </div>
                                );
                              })}
                            </div>
                            <span className="text-xs sm:text-xs font-medium hidden sm:inline">
                              {composioMCPs.length === 1 ? '1 Integration' : `${composioMCPs.length} Integrations`}
                            </span>
                          </>
                        );
                      })()}
                    </button>

                    <div className="w-px h-4 bg-border/60" />

                    <button
                      onClick={() => {
                        setConfigModalTab('instructions');
                        setConfigModalOpen(true);
                      }}
                      className="flex items-center gap-1 sm:gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2 sm:px-2.5 py-2 sm:py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0 min-h-[44px] sm:min-h-auto"
                    >
                      <Brain className="h-3.5 w-3.5 flex-shrink-0" />
                      <span className="text-xs font-medium hidden sm:inline">System</span>
                    </button>

                    <div className="w-px h-4 bg-border/60" />

                    <button
                      onClick={() => {
                        setConfigModalTab('knowledge');
                        setConfigModalOpen(true);
                      }}
                      className="flex items-center gap-1 sm:gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2 sm:px-2.5 py-2 sm:py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0 min-h-[44px] sm:min-h-auto"
                    >
                      <Database className="h-3.5 w-3.5 flex-shrink-0" />
                      <span className="text-xs font-medium hidden sm:inline">Knowledge</span>
                    </button>

                    <div className="w-px h-4 bg-border/60" />

                    {/* Model Selector */}
                    <ModelToggle
                      selectedModel={selectedModel}
                      onModelChange={handleModelChange}
                      canAccessModel={canAccessModel}
                      subscriptionStatus={subscriptionStatus}
                    />
                  </div>

                  {/* Ideas Button - Right aligned */}
                  {onToggleSuggestions && (
                    <button
                      onClick={onToggleSuggestions}
                      className="flex items-center gap-1 sm:gap-1 text-muted-foreground hover:text-foreground transition-all duration-200 px-2 sm:px-2.5 py-2 sm:py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 min-h-[44px] sm:min-h-auto flex-shrink-0"
                    >
                      <Lightbulb className="h-4 w-4 sm:h-3.5 sm:w-3.5" />
                      <span className="text-xs font-medium hidden sm:inline">
                        {showSuggestions ? 'Hide' : 'Ideas'}
                      </span>
                      <motion.div
                        animate={{ rotate: showSuggestions ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ChevronDown className="h-4 w-4 sm:h-3 sm:w-3" />
                      </motion.div>
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* {isAgentRunning && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="pb-4 -mt-4 w-full flex items-center justify-center"
          >
            <div className="text-xs text-muted-foreground flex items-center gap-2">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>{agentName ? `${agentName} is working...` : 'Atlas is working...'}</span>
            </div>
          </motion.div>
        )} */}

        {/* Agent Configuration Modal */}
        <AgentConfigModal
          isOpen={configModalOpen}
          onOpenChange={setConfigModalOpen}
          selectedAgentId={selectedAgentId}
          onAgentSelect={onAgentSelect}
          initialTab={configModalTab}
          hideAgentSelector={hideAgentSelection}
        />

        {/* MCP Apps Modal for Integrations */}
        <MCPAppsModal
          isOpen={integrationsModalOpen}
          onClose={() => setIntegrationsModalOpen(false)}
          apps={apps}
          connectedApps={connectedApps}
          connectingApps={connectingApps}
          disconnectingApps={disconnectingApps}
          isLoading={appsLoading}
          onConnect={handleMCPConnect}
          onDisconnect={handleMCPDisconnect}
          selectedAgentId={selectedAgentId}
          agentConnectedApps={agentConnectedApps}
          defaultAgentConnectedApps={defaultAgentConnectedApps}
          onToggleAgentApp={handleToggleAgentApp}
        />

      </div>
    );
  },
);

ChatInput.displayName = 'ChatInput';
