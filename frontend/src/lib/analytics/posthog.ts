import posthog from 'posthog-js';
import { User } from '@supabase/supabase-js';

/**
 * Centralized PostHog analytics service
 * Handles all PostHog tracking, identification, and event management
 */
export class PostHogService {
  /**
   * Identify a user in PostHog with their profile data
   */
  static identifyUser(user: User) {
    if (!user) return;

    const userProperties = {
      email: user.email,
      signup_date: user.created_at,
      signup_method: user.app_metadata?.provider || 'email',
      user_id: user.id,
      // Add any other user properties you want to track
      last_sign_in: user.last_sign_in_at,
      email_confirmed: user.email_confirmed_at ? true : false,
    };

    // Debug log to verify email is being passed
    console.log('PostHog identify:', user.id, userProperties);

    posthog.identify(user.id, userProperties);
  }

  /**
   * Track user signup event
   */
  static trackSignup(user: User) {
    posthog.capture('user_signed_up', {
      user_id: user.id,
      email: user.email,
      signup_method: user.app_metadata?.provider || 'email',
      created_at: user.created_at,
      is_first_time_signup: true
    });
  }

  /**
   * Track user signin event
   */
  static trackSignin(user: User) {
    posthog.capture('user_signed_in', {
      user_id: user.id,
      email: user.email,
      signin_method: user.app_metadata?.provider || 'email',
      last_sign_in: user.last_sign_in_at
    });
  }

  /**
   * Track page views for conversion funnel analysis
   */
  static trackPageView(page: string, properties?: Record<string, any>) {
    posthog.capture('$pageview', {
      $current_url: window.location.href,
      page_name: page,
      referrer: document.referrer,
      timestamp: new Date().toISOString(),
      ...properties
    });
  }

  /**
   * Track landing page visits (for signup conversion tracking)
   */
  static trackLandingPageVisit(properties?: Record<string, any>) {
    posthog.capture('landing_page_visit', {
      $current_url: window.location.href,
      referrer: document.referrer,
      utm_source: new URLSearchParams(window.location.search).get('utm_source'),
      utm_medium: new URLSearchParams(window.location.search).get('utm_medium'),
      utm_campaign: new URLSearchParams(window.location.search).get('utm_campaign'),
      is_first_visit: !localStorage.getItem('posthog_visited_before'),
      timestamp: new Date().toISOString(),
      ...properties
    });

    // Mark that user has visited before
    localStorage.setItem('posthog_visited_before', 'true');
  }

  /**
   * Track user message submission
   */
  static trackUserMessage(properties: {
    message_length: number;
    has_files: boolean;
    file_count: number;
    model_name: string;
    thinking_enabled: boolean;
    agent_id?: string;
    agent_name?: string;
  }) {
    posthog.capture('user_message_sent', {
      ...properties,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Track agent template events
   */
  static trackAgentTemplate(event: string, properties: Record<string, any>) {
    posthog.capture(event, properties);
  }

  /**
   * Track dashboard events
   */
  static trackDashboardEvent(event: string, properties: Record<string, any>) {
    posthog.capture(event, properties);
  }

  /**
   * Reset user identity (for logout)
   */
  static resetUser() {
    posthog.reset();
  }
}
