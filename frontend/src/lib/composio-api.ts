/**
 * Centralized Composio MCP API Service
 *
 * This service provides a centralized interface for all Composio MCP operations
 * using the new v3 endpoints with simplified authentication flow.
 */

import { createClient } from '@/lib/supabase/client';
import {
  ComposioApp,
  ComposioConnection,
  CreateComposioConnectionRequest,
  CreateComposioConnectionResponse,
  ListUserConnectionsResponse,
  DeleteConnectionResponse,
  GetSupportedAppsResponse,
  ComposioHealthResponse,
  ComposioAPIError,
  ComposioAppKey,
  ComposioConnectionStatus,
} from '@/types/composio';

// Remove /api suffix if present since we'll add it in our endpoints
const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || '';
const API_URL = BACKEND_URL.replace(/\/api$/, '');

// Internal response types for API calls
interface InitiateConnectionResponse {
  success: boolean;
  redirect_url: string;
  connection_request_id: string;
  app_key: string;
  error?: string;
}

interface ConnectionStatusResponse {
  success: boolean;
  is_connected: boolean;
  connection_id?: string;
  toolsets?: string[];
}

interface ListConnectionsResponse {
  success: boolean;
  connections: ComposioConnection[];
  error?: string;
}

interface DeleteConnectionApiResponse {
  success: boolean;
  error?: string;
}

interface SupportedAppsResponse {
  success: boolean;
  integrations: any[];
  total: number;
  error?: string;
}

/**
 * Centralized Composio MCP API Service - V3 Endpoints
 */
export class ComposioMCPService {
  /**
   * Get authenticated headers for API requests
   */
  private static async getAuthHeaders(): Promise<Record<string, string>> {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new ComposioAPIError('No authentication token available', 401, 'NO_AUTH_TOKEN');
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    };
  }

  /**
   * Handle API response and errors
   */
  private static async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      let errorData;

      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { message: errorText };
      }

      throw new ComposioAPIError(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        errorData.errorCode
      );
    }

    return response.json();
  }

  /**
   * Initiate a new Composio v3 connection and return redirect URL immediately
   */
  static async initiateConnection(appKey: ComposioAppKey): Promise<{
    redirect_url: string;
    connection_request_id: string;
    app_key: string;
  }> {
    if (!API_URL) {
      throw new ComposioAPIError('Backend URL not configured', 500, 'NO_BACKEND_URL');
    }

    const headers = await this.getAuthHeaders();
    const requestBody = { app_key: appKey };

    console.log(`[ComposioAPI] Initiating v3 connection for app: ${appKey}`);

    const response = await fetch(`${API_URL}/api/composio-v3/initiate-connection`, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
    });

    const result = await this.handleResponse<InitiateConnectionResponse>(response);

    console.log('[ComposioAPI] Initiate connection response:', result);

    if (!result.success) {
      console.error('[ComposioAPI] Initiate connection failed:', result.error);
      throw new ComposioAPIError(result.error || 'Failed to initiate connection', 400, 'INITIATE_FAILED');
    }

    return {
      redirect_url: result.redirect_url,
      connection_request_id: result.connection_request_id,
      app_key: result.app_key,
    };
  }

  /**
   * Check connection status and wait for completion (used after OAuth)
   */
  static async checkConnectionStatus(
    connectionRequestId: string,
    appKey: ComposioAppKey
  ): Promise<{
    success: boolean;
    is_connected: boolean;
    connection_id?: string;
    toolsets?: string[];
  }> {
    if (!API_URL) {
      throw new ComposioAPIError('Backend URL not configured', 500, 'NO_BACKEND_URL');
    }

    const headers = await this.getAuthHeaders();
    const requestBody = {
      connection_request_id: connectionRequestId,
      app_key: appKey,
    };

    console.log(`[ComposioAPI] Checking connection status for: ${appKey}`);

    const response = await fetch(`${API_URL}/api/composio-v3/check-connection-status`, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
    });

    const result = await this.handleResponse<ConnectionStatusResponse>(response);

    console.log('[ComposioAPI] Connection status response:', result);

    return {
      success: result.success,
      is_connected: result.is_connected,
      connection_id: result.connection_id,
      toolsets: result.toolsets,
    };
  }

  /**
   * List all Composio v3 connections for the current user
   */
  static async listUserConnections(): Promise<ComposioConnection[]> {
    if (!API_URL) {
      throw new ComposioAPIError('Backend URL not configured', 500, 'NO_BACKEND_URL');
    }

    const headers = await this.getAuthHeaders();

    console.log('[ComposioAPI] Fetching user v3 connections');

    const response = await fetch(`${API_URL}/api/composio-v3/user-connections`, {
      method: 'GET',
      headers,
    });

    const result = await this.handleResponse<ListConnectionsResponse>(response);

    console.log('[ComposioAPI] List v3 connections response:', result);

    if (!result.success) {
      console.error('[ComposioAPI] List connections failed:', result);
      throw new ComposioAPIError('Failed to fetch connections', 400, 'FETCH_FAILED');
    }

    console.log('[ComposioAPI] Found v3 connections:', result.connections);
    return result.connections;
  }

  /**
   * Delete a Composio v3 connection
   */
  static async deleteConnection(appKey: ComposioAppKey): Promise<boolean> {
    if (!API_URL) {
      throw new ComposioAPIError('Backend URL not configured', 500, 'NO_BACKEND_URL');
    }

    const headers = await this.getAuthHeaders();

    console.log(`[ComposioAPI] Deleting v3 connection for app: ${appKey}`);

    const response = await fetch(`${API_URL}/api/composio-v3/connection/${appKey}`, {
      method: 'DELETE',
      headers,
    });

    const result = await this.handleResponse<DeleteConnectionApiResponse>(response);

    return result.success;
  }

  /**
   * Get list of supported Composio integrations (v3)
   */
  static async getSupportedApps(): Promise<GetSupportedAppsResponse> {
    if (!API_URL) {
      throw new ComposioAPIError('Backend URL not configured', 500, 'NO_BACKEND_URL');
    }

    console.log('[ComposioAPI] Fetching supported v3 integrations');

    const response = await fetch(`${API_URL}/api/composio-v3/supported-integrations`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await ComposioMCPService.handleResponse<SupportedAppsResponse>(response);

    if (!result.success) {
      throw new ComposioAPIError('Failed to fetch supported integrations', 400, 'FETCH_APPS_FAILED');
    }

    // Transform v3 response to match existing interface
    // Backend returns 'app_key' but frontend expects 'key'
    const transformedApps = result.integrations.map((integration: any) => ({
      ...integration,
      key: integration.app_key, // Map app_key to key for frontend compatibility
    }));

    return {
      success: result.success,
      apps: transformedApps,
      total: result.total,
      message: 'Successfully fetched supported integrations',
    };
  }

  /**
   * Health check for Composio v3 service
   */
  static async healthCheck(): Promise<ComposioHealthResponse> {
    if (!API_URL) {
      throw new ComposioAPIError('Backend URL not configured', 500, 'NO_BACKEND_URL');
    }

    console.log('[ComposioAPI] Health check for v3 service');

    const response = await fetch(`${API_URL}/api/composio-v3/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return await this.handleResponse<ComposioHealthResponse>(response);
  }

  /**
   * Check if a specific app is connected
   */
  static async isAppConnected(appKey: ComposioAppKey): Promise<boolean> {
    try {
      const connections = await this.listUserConnections();
      return connections.some(conn =>
        conn.app_key === appKey &&
        conn.status === 'connected'
      );
    } catch (error) {
      console.error(`[ComposioAPI] Error checking if ${appKey} is connected:`, error);
      return false;
    }
  }

  /**
   * Get connection status for a specific app
   */
  static async getConnectionStatus(appKey: ComposioAppKey): Promise<ComposioConnection | null> {
    try {
      const connections = await this.listUserConnections();
      return connections.find(conn => conn.app_key === appKey) || null;
    } catch (error) {
      console.error(`[ComposioAPI] Error getting connection status for ${appKey}:`, error);
      return null;
    }
  }

  /**
   * Legacy method for backward compatibility - now just checks connection status
   */
  static async refreshConnection(appKey: ComposioAppKey): Promise<boolean> {
    try {
      console.log(`[ComposioAPI] Checking connection status for ${appKey} (v3 flow)`);
      const connection = await this.getConnectionStatus(appKey);
      return connection?.status === 'connected' || false;
    } catch (error) {
      console.error(`[ComposioAPI] Error refreshing connection for ${appKey}:`, error);
      return false;
    }
  }

  /**
   * Legacy method for backward compatibility - now uses initiate connection
   */
  static async createConnection(appKey: ComposioAppKey): Promise<ComposioConnection> {
    const initResult = await this.initiateConnection(appKey);

    // Return a basic connection object for compatibility
    return {
      id: initResult.connection_request_id,
      user_id: '', // Will be populated by backend
      qualified_name: `composio/${appKey}`,
      app_key: appKey,
      app_name: appKey,
      mcp_url: '',
      auth_url: initResult.redirect_url,
      session_uuid: initResult.connection_request_id,
      status: 'pending' as ComposioConnectionStatus,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      scope: `composio_${appKey}`,
    };
  }

  /**
   * Legacy method for backward compatibility - now uses initiate connection
   */
  static async reconnectApp(appKey: ComposioAppKey): Promise<ComposioConnection> {
    // For v3, reconnection is the same as initial connection
    return this.createConnection(appKey);
  }
}

/**
 * Utility functions for working with Composio data
 */
export const ComposioUtils = {
  /**
   * Filter apps by category
   */
  filterAppsByCategory: (apps: ComposioApp[], category: string): ComposioApp[] => {
    return apps.filter(app => app.category === category);
  },

  /**
   * Search apps by name or description
   */
  searchApps: (apps: ComposioApp[], query: string): ComposioApp[] => {
    const lowercaseQuery = query.toLowerCase();
    return apps.filter(app =>
      app.name.toLowerCase().includes(lowercaseQuery) ||
      app.description.toLowerCase().includes(lowercaseQuery) ||
      app.key.toLowerCase().includes(lowercaseQuery)
    );
  },

  /**
   * Get app by key
   */
  getAppByKey: (apps: ComposioApp[], appKey: ComposioAppKey): ComposioApp | undefined => {
    return apps.find(app => app.key === appKey);
  },

  /**
   * Group apps by category
   */
  groupAppsByCategory: (apps: ComposioApp[]): Record<string, ComposioApp[]> => {
    return apps.reduce((groups, app) => {
      const category = app.category || 'other';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(app);
      return groups;
    }, {} as Record<string, ComposioApp[]>);
  },

  /**
   * Format qualified name for Composio connections
   */
  formatQualifiedName: (appKey: ComposioAppKey): string => {
    return `composio/${appKey}`;
  },

  /**
   * Extract app key from qualified name
   */
  extractAppKey: (qualifiedName: string): ComposioAppKey => {
    return qualifiedName.replace('composio/', '');
  },
};
