import React from 'react';
import {
  Search, ExternalLink, ArrowRight, Check, Loader2,
  Terminal, FolderOpen, Globe, Rocket, Link, Eye, BarChart3,
  <PERSON><PERSON>, Zap
} from 'lucide-react';

// Custom ToolCase icon component
export const ToolCase = React.forwardRef<SVGSVGElement, { className?: string; [key: string]: any }>(
  ({ className, ...props }, ref) =>
    React.createElement('svg', {
      ref,
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round",
      className,
      ...props
    }, [
      React.createElement('path', { key: 'path1', d: "M10 15h4" }),
      React.createElement('path', { key: 'path2', d: "m14.817 10.995-.971-1.45 1.034-1.232a2 2 0 0 0-2.025-3.238l-1.82.364L9.91 3.885a2 2 0 0 0-3.625.748L6.141 6.55l-1.725.426a2 2 0 0 0-.19 3.756l.657.27" }),
      React.createElement('path', { key: 'path3', d: "m18.822 10.995 2.26-5.38a1 1 0 0 0-.557-1.318L16.954 2.9a1 1 0 0 0-1.281.533l-.924 2.122" }),
      React.createElement('path', { key: 'path4', d: "M4 12.006A1 1 0 0 1 4.994 11H19a1 1 0 0 1 1 1v7a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2z" })
    ])
);

ToolCase.displayName = "ToolCase";
import {
  SiGithub, SiTrello, SiAsana, SiJira, SiDiscord, SiLinkedin,
  SiYoutube, SiSpotify, SiDropbox, SiMailchimp, SiStripe,
  SiPaypal, SiShopify, SiWordpress, SiAdobe, SiCanva, SiFigma
} from 'react-icons/si';
import { FaMicrosoft } from 'react-icons/fa';

// Professional MCP server icon URLs with white backgrounds
export const mcpServerImageIcons: Record<string, string> = {
  'gmail': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/f1fd7a85df9be926c65e7c16d19b3f6a_low_res_Gmail.png',
  'googlesheets': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/9389591980621f2fdc40e4d4ac28a2be_low_res_Google_Sheets.png',
  'googledocs': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/a46c6f0820ac26fb8ca0a5440b149c0e_low_res_Google_Docs.png',
  'googledrive': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/73ec025cea371595fef1dc8c1853c7b7_low_res_Google_Drive.png',
  'googlecalendar': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/8939e05daafb23f2d39a484ff86d790a_low_res_Google_Calendar.png',
  'notion': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/6d874d18130a6a555fb6923beeb5fcfd_low_res_Notion.png',
  'slack': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/b09b87f39d6b1258327f6d51d07358c1_low_res_Slack.png',
  'microsoft': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/1538b4cbfb154231d25f8a5866ca4147_low_res_Microsoft%20Teams.png',
  'twitter': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/4b5f3821b6eb348eeacbdbeca8ded3b0_low_res_X.png',
  'linear': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/d79d7c78d0bfcc979a5eff37761f27a4_low_res_Linear_Blue.png',
  'reddit': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/47c001d597a20322a0d9a7ed4823ff66_low_res_Reddit.png',
  'hubspot': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/b72e031bef3b7644bc89396cd9e64ea1_low_res_Hubspot.png',
  'zoom': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/15dbf3650101380e8cb54641a0fb9995_low_res_Zoom.png',
  'salesforce': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/39a20fd0a8388066267047ca765ba9b3_low_res_Salesforce.png',
  'airtable': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/7cc245b5b4f0032f83f49dbdb69b9f4a_Xxc1oZIU9l.png',
  'clickup': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/dfc4b1c4b9ab511b70ddb1ccc3af47e7_LWEz6qlVmb.png',
  'outlook': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/bb292bdeda111a8455af74f8a2d85fdc_dGlSIRTK7r.png',
  'excel': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/7ff3718c926481b67d1580766b50937b_low_res_Excel_3D_White.png',
  'supabase': 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/b5b2b2b2b2b2b2b2b2b2b2b2b2b2b2b2_low_res_Supabase.png', // Placeholder - need actual Supabase icon
};

// Create a React component for image icons - properly sized to respect className
const createImageIcon = (src: string, alt: string) => {
  const ImageIcon = ({ className, style, ...props }: { className?: string; style?: React.CSSProperties; [key: string]: any }) =>
    React.createElement('img', {
      src,
      alt,
      className,
      style: {
        objectFit: 'contain' as const,
        ...style
      },
      ...props
    });

  ImageIcon.displayName = `ImageIcon(${alt})`;
  return ImageIcon;
};

// Core integration icon mapping - now using professional image icons for MCP servers
export const integrationIcons: Record<string, React.ComponentType<any>> = {
  // MCP server icons with professional images
  'gmail': createImageIcon(mcpServerImageIcons.gmail, 'Gmail'),
  'google': createImageIcon(mcpServerImageIcons.googlesheets, 'Google Sheets'), // Default to Sheets for google
  'googledocs': createImageIcon(mcpServerImageIcons.googledocs, 'Google Docs'),
  'googlesheets': createImageIcon(mcpServerImageIcons.googlesheets, 'Google Sheets'),
  'googlecalendar': createImageIcon(mcpServerImageIcons.googlecalendar, 'Google Calendar'),
  'googledrive': createImageIcon(mcpServerImageIcons.googledrive, 'Google Drive'),
  'notion': createImageIcon(mcpServerImageIcons.notion, 'Notion'),
  'linear': createImageIcon(mcpServerImageIcons.linear, 'Linear'),
  'hubspot': createImageIcon(mcpServerImageIcons.hubspot, 'HubSpot'),
  'twitter': createImageIcon(mcpServerImageIcons.twitter, 'X (Twitter)'),
  'slack': createImageIcon(mcpServerImageIcons.slack, 'Slack'),
  'microsoft': createImageIcon(mcpServerImageIcons.microsoft, 'Microsoft Teams'),
  'zoom': createImageIcon(mcpServerImageIcons.zoom, 'Zoom'),
  'salesforce': createImageIcon(mcpServerImageIcons.salesforce, 'Salesforce'),
  'reddit': createImageIcon(mcpServerImageIcons.reddit, 'Reddit'),
  'airtable': createImageIcon(mcpServerImageIcons.airtable, 'Airtable'),
  'clickup': createImageIcon(mcpServerImageIcons.clickup, 'ClickUp'),
  'outlook': createImageIcon(mcpServerImageIcons.outlook, 'Outlook'),
  'excel': createImageIcon(mcpServerImageIcons.excel, 'Excel'),
  'supabase': createImageIcon(mcpServerImageIcons.supabase, 'Supabase'),

  // Keep Simple Icons for services not in our MCP server list
  'figma': SiFigma,
  'github': SiGithub,
  'trello': SiTrello,
  'asana': SiAsana,
  'jira': SiJira,
  'discord': SiDiscord,
  'linkedin': SiLinkedin,
  'youtube': SiYoutube,
  'spotify': SiSpotify,
  'dropbox': SiDropbox,
  'mailchimp': SiMailchimp,
  'stripe': SiStripe,
  'paypal': SiPaypal,
  'shopify': SiShopify,
  'wordpress': SiWordpress,
  'adobe': SiAdobe,
  'canva': SiCanva,
};

// AgentPress tool icon mapping
export const agentpressIcons: Record<string, React.ComponentType<any>> = {
  'sb_shell_tool': Terminal,
  'sb_files_tool': FolderOpen,
  'sb_browser_tool': Globe,
  'sb_deploy_tool': Rocket,
  'sb_expose_tool': Link,
  'web_search_tool': Search,
  'sb_vision_tool': Eye,
  'data_providers_tool': BarChart3,
};

// State-specific icons for tool mentions
export const mentionStateIcons = {
  'available_to_connect': ExternalLink,
  'connected_to_account': ArrowRight,
  'connected_to_agent': Check,
  'loading': Loader2,
} as const;

export type MentionState = keyof typeof mentionStateIcons;

// Tool types for classification
export type ToolType = 'configured_mcp' | 'custom_mcp' | 'composio_mcp' | 'agentpress';

// Interface for tool objects
export interface ToolInfo {
  name: string;
  displayName?: string;
  qualifiedName?: string;
  isCustom?: boolean;
  type?: ToolType;
  appKey?: string;
}

/**
 * Get icon component for MCP tools based on name and metadata
 */
export function getMCPIconComponent(tool: ToolInfo): React.ComponentType<any> {
  // Handle undefined names gracefully
  if (!tool.name) {
    return Search; // Default fallback for undefined names
  }

  const lowerName = tool.name.toLowerCase();
  const qualifiedName = tool.qualifiedName?.toLowerCase() || '';
  const displayName = tool.displayName?.toLowerCase() || '';
  const appKey = tool.appKey?.toLowerCase() || '';

  // Handle AgentPress tools first
  if (tool.type === 'agentpress') {
    const agentpressIcon = agentpressIcons[lowerName];
    if (agentpressIcon) return agentpressIcon;
    return Wrench; // Default for unknown agentpress tools
  }

  // Helper function to normalize qualified names for consistent matching
  const normalizeQualifiedName = (qName: string): string => {
    // Remove composio/ prefix and normalize underscores to match our icon keys
    return qName.replace(/^composio\//, '').replace(/_/g, '');
  };

  // Helper function to get icon by key with fallbacks
  const getIconByKey = (key: string): React.ComponentType<any> | null => {
    // Direct match
    if (integrationIcons[key]) return integrationIcons[key];

    // Try with underscores converted to nothing (google_sheets -> googlesheets)
    const noUnderscores = key.replace(/_/g, '');
    if (integrationIcons[noUnderscores]) return integrationIcons[noUnderscores];

    return null;
  };

  // Priority 1: Use appKey for Composio v3 connections (most reliable)
  if (appKey) {
    const icon = getIconByKey(appKey);
    if (icon) return icon;
  }

  // Priority 2: Handle qualified names (both template format and composio format)
  if (qualifiedName) {
    const normalizedQualifiedName = normalizeQualifiedName(qualifiedName);

    // Try direct qualified name match
    const qualifiedIcon = getIconByKey(qualifiedName);
    if (qualifiedIcon) return qualifiedIcon;

    // Try normalized qualified name
    const normalizedIcon = getIconByKey(normalizedQualifiedName);
    if (normalizedIcon) return normalizedIcon;

    // Handle specific qualified name patterns
    if (qualifiedName.includes('exa')) return Search;
    if (qualifiedName.includes('github')) return integrationIcons['github'];
    if (qualifiedName.includes('notion')) return integrationIcons['notion'];
    if (qualifiedName.includes('slack')) return integrationIcons['slack'];
    if (qualifiedName.includes('linear')) return integrationIcons['linear'];
    if (qualifiedName.includes('figma')) return integrationIcons['figma'];
    if (qualifiedName.includes('desktop-commander')) return integrationIcons['microsoft'];
    if (qualifiedName.includes('filesystem')) return Search;
    if (qualifiedName.includes('gmail')) return integrationIcons['gmail'];
    if (qualifiedName.includes('google')) {
      if (qualifiedName.includes('docs')) return integrationIcons['googledocs'];
      if (qualifiedName.includes('sheets')) return integrationIcons['googlesheets'];
      if (qualifiedName.includes('calendar')) return integrationIcons['googlecalendar'];
      if (qualifiedName.includes('drive')) return integrationIcons['googledrive'];
      return integrationIcons['google']; // Default to sheets
    }
    if (qualifiedName.includes('hubspot')) return integrationIcons['hubspot'];
    if (qualifiedName.includes('zoom')) return integrationIcons['zoom'];
    if (qualifiedName.includes('salesforce')) return integrationIcons['salesforce'];
    if (qualifiedName.includes('airtable')) return integrationIcons['airtable'];
    if (qualifiedName.includes('clickup')) return integrationIcons['clickup'];
    if (qualifiedName.includes('outlook')) return integrationIcons['outlook'];
    if (qualifiedName.includes('excel')) return integrationIcons['excel'];
    if (qualifiedName.includes('reddit')) return integrationIcons['reddit'];
    if (qualifiedName.includes('twitter') || qualifiedName.includes('x.com')) return integrationIcons['twitter'];
    if (qualifiedName.includes('supabase')) return integrationIcons['supabase'];
  }

  // Priority 3: Check all name variations (name, displayName)
  const namesToCheck = [lowerName, displayName].filter(Boolean);

  for (const nameToCheck of namesToCheck) {
    // Try direct name match first
    const directIcon = getIconByKey(nameToCheck);
    if (directIcon) return directIcon;

    // Gmail variations
    if (nameToCheck.includes('gmail')) return integrationIcons['gmail'];

    // Google services
    if (nameToCheck.includes('google')) {
      if (nameToCheck.includes('docs')) return integrationIcons['googledocs'];
      if (nameToCheck.includes('sheets')) return integrationIcons['googlesheets'];
      if (nameToCheck.includes('calendar')) return integrationIcons['googlecalendar'];
      if (nameToCheck.includes('drive')) return integrationIcons['googledrive'];
      return integrationIcons['google']; // Default to sheets
    }

    // MCP server integrations with professional image icons
    if (nameToCheck.includes('notion')) return integrationIcons['notion'];
    if (nameToCheck.includes('linear')) return integrationIcons['linear'];
    if (nameToCheck.includes('hubspot')) return integrationIcons['hubspot'];
    if (nameToCheck.includes('twitter') || nameToCheck.includes('x.com') || nameToCheck === 'x') return integrationIcons['twitter'];
    if (nameToCheck.includes('slack')) return integrationIcons['slack'];
    if (nameToCheck.includes('microsoft') || nameToCheck.includes('teams')) return integrationIcons['microsoft'];
    if (nameToCheck.includes('zoom')) return integrationIcons['zoom'];
    if (nameToCheck.includes('salesforce')) return integrationIcons['salesforce'];
    if (nameToCheck.includes('reddit')) return integrationIcons['reddit'];
    if (nameToCheck.includes('airtable')) return integrationIcons['airtable'];
    if (nameToCheck.includes('clickup')) return integrationIcons['clickup'];
    if (nameToCheck.includes('outlook')) return integrationIcons['outlook'];
    if (nameToCheck.includes('excel')) return integrationIcons['excel'];

    // Other integrations still using Simple Icons
    if (nameToCheck.includes('figma')) return integrationIcons['figma'];
    if (nameToCheck.includes('github')) return integrationIcons['github'];
    if (nameToCheck.includes('trello')) return integrationIcons['trello'];
    if (nameToCheck.includes('asana')) return integrationIcons['asana'];
    if (nameToCheck.includes('jira')) return integrationIcons['jira'];
    if (nameToCheck.includes('discord')) return integrationIcons['discord'];
    if (nameToCheck.includes('linkedin')) return integrationIcons['linkedin'];
    if (nameToCheck.includes('youtube')) return integrationIcons['youtube'];
    if (nameToCheck.includes('spotify')) return integrationIcons['spotify'];
    if (nameToCheck.includes('dropbox')) return integrationIcons['dropbox'];
    if (nameToCheck.includes('mailchimp')) return integrationIcons['mailchimp'];
    if (nameToCheck.includes('stripe')) return integrationIcons['stripe'];
    if (nameToCheck.includes('paypal')) return integrationIcons['paypal'];
    if (nameToCheck.includes('shopify')) return integrationIcons['shopify'];
    if (nameToCheck.includes('wordpress')) return integrationIcons['wordpress'];
    if (nameToCheck.includes('adobe')) return integrationIcons['adobe'];
    if (nameToCheck.includes('canva')) return integrationIcons['canva'];
    if (nameToCheck.includes('supabase')) return integrationIcons['supabase'];
  }

  // Default fallback
  return Search;
}

/**
 * Get icon component for tool mention states
 */
export function getMentionStateIcon(state: MentionState): React.ComponentType<any> {
  return mentionStateIcons[state];
}

/**
 * Get icon for Composio apps using the same logic as MCP tools
 */
export function getComposioAppIcon(app: { name: string; key: string; icon?: string }): React.ComponentType<any> {
  // If we have a React icon component name, try to resolve it
  if (app.icon && !app.icon.startsWith('http') && !app.icon.match(/[\u{1f300}-\u{1f5ff}\u{1f900}-\u{1f9ff}\u{1f600}-\u{1f64f}\u{1f680}-\u{1f6ff}\u{2600}-\u{26ff}\u{2700}-\u{27bf}]/u)) {
    // This might be a component name, but for now we'll fall through to name-based matching
  }

  // Use the same logic as MCP tools
  return getMCPIconComponent({
    name: app.name,
    displayName: app.name,
    appKey: app.key,
    type: 'composio_mcp'
  });
}

/**
 * Helper to get all MCP tools for an agent with their icons
 */
export function getAgentMCPTools(agent: any): Array<{name: string, IconComponent: React.ComponentType<any>}> {
  if (!agent) return [];

  const tools: Array<{name: string, IconComponent: React.ComponentType<any>}> = [];

  // Add configured MCPs
  if (agent.configured_mcps) {
    agent.configured_mcps.forEach((mcp: any) => {
      // Extract name, handling different field names
      const name = mcp.name || mcp.app_name || mcp.displayName || 'Unknown MCP';

      tools.push({
        name: name,
        IconComponent: getMCPIconComponent({
          name: name,
          displayName: mcp.displayName || mcp.app_name || mcp.name,
          qualifiedName: mcp.qualifiedName || mcp.qualified_name || mcp.server_url,
          appKey: mcp.app_key || mcp.appKey,
          isCustom: false,
          type: 'configured_mcp'
        })
      });
    });
  }

  // Add custom MCPs (including Composio v3 connections)
  if (agent.custom_mcps) {
    agent.custom_mcps.forEach((mcp: any) => {
      // Extract name, handling different field names (Composio v3 uses app_name)
      const name = mcp.name || mcp.app_name || mcp.displayName || 'Unknown MCP';

      // Extract qualified name from metadata or other sources
      const qualifiedName = mcp.qualifiedName ||
                           mcp.qualified_name ||
                           mcp._metadata?.qualified_name ||
                           mcp.server_url;

      // Extract app key from various possible locations
      const appKey = mcp.app_key ||
                     mcp.appKey ||
                     mcp._metadata?.app_key;

      tools.push({
        name: name,
        IconComponent: getMCPIconComponent({
          name: name,
          displayName: mcp.displayName || mcp.app_name || mcp.name,
          qualifiedName: qualifiedName,
          appKey: appKey,
          isCustom: true,
          type: 'custom_mcp'
        })
      });
    });
  }

  return tools;
}

/**
 * Resolve tool icon from various tool formats used across the app
 */
export function resolveToolIcon(tool: any): React.ComponentType<any> {
  // Handle ClassifiedMCPTool format
  if (tool.type && tool.name) {
    return getMCPIconComponent({
      name: tool.name,
      displayName: tool.displayName,
      qualifiedName: tool.qualifiedName,
      isCustom: tool.type === 'custom_mcp',
      type: tool.type,
      appKey: tool.appKey
    });
  }

  // Handle tool ID format (from mention markup)
  if (tool.id) {
    const toolId = tool.id;
    const displayName = tool.displayName || tool.name || '';

    // Parse tool type from ID
    let toolType: ToolType = 'custom_mcp';
    let toolName = displayName;

    if (toolId.startsWith('configured_mcp_') || toolId.startsWith('default_configured_mcp_')) {
      toolType = 'configured_mcp';
      toolName = toolId.replace(/^(default_)?configured_mcp_/, '');
    } else if (toolId.startsWith('custom_mcp_') || toolId.startsWith('default_custom_mcp_')) {
      toolType = 'custom_mcp';
      toolName = toolId.replace(/^(default_)?custom_mcp_/, '');
    } else if (toolId.includes('composio') || toolId.startsWith('available_composio_')) {
      toolType = 'composio_mcp';
      toolName = toolId.replace(/^available_composio_/, '');
    } else if (toolId.startsWith('agentpress_')) {
      toolType = 'agentpress';
      toolName = toolId.replace('agentpress_', '');
    }

    return getMCPIconComponent({
      name: toolName,
      displayName: displayName,
      isCustom: toolType === 'custom_mcp',
      type: toolType
    });
  }

  // Handle simple tool objects
  if (tool.name) {
    return getMCPIconComponent({
      name: tool.name,
      displayName: tool.displayName || tool.name,
      qualifiedName: tool.qualifiedName,
      isCustom: tool.isCustom,
      type: tool.type
    });
  }

  // Fallback
  return Search;
}
