'use client';

import { useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';

export type InstallationState = 'idle' | 'installing' | 'completed' | 'failed';

export interface TemplateInstallationState {
  templateId: string | null;
  state: InstallationState;
  error: string | null;
  installedAgentId: string | null;
}

/**
 * Centralized template installation state management hook
 * Prevents infinite loops and duplicate installations
 */
export function useTemplateInstallation() {
  const [installationState, setInstallationState] = useState<TemplateInstallationState>({
    templateId: null,
    state: 'idle',
    error: null,
    installedAgentId: null,
  });

  // Track attempted installations to prevent duplicates
  const attemptedInstallations = useRef<Set<string>>(new Set());

  const startInstallation = useCallback((templateId: string) => {
    let canInstall = true;
    
    setInstallationState(prev => {
      if (prev.state === 'installing') {
        console.log('Installation already in progress, ignoring request');
        canInstall = false;
        return prev;
      }
      
      if (attemptedInstallations.current.has(templateId)) {
        console.log(`Template ${templateId} already attempted, ignoring request`);
        canInstall = false;
        return prev;
      }
      
      if (canInstall) {
        console.log(`Starting installation for template: ${templateId}`);
        attemptedInstallations.current.add(templateId);
        
        return {
          templateId,
          state: 'installing' as const,
          error: null,
          installedAgentId: null,
        };
      }
      
      return prev;
    });
    
    return canInstall;
  }, []);

  const completeInstallation = useCallback((installedAgentId: string) => {
    setInstallationState(prev => ({
      ...prev,
      state: 'completed',
      installedAgentId,
    }));
    
    toast.success('Agent template installed successfully!');
  }, []);

  const failInstallation = useCallback((error: string, shouldRetry: boolean = false) => {
    setInstallationState(prev => {
      // If this is a retryable error, remove from attempted set
      if (shouldRetry && prev.templateId) {
        attemptedInstallations.current.delete(prev.templateId);
      }
      
      return {
        ...prev,
        state: 'failed',
        error,
      };
    });

    toast.error(error);
  }, []);

  const resetInstallation = useCallback(() => {
    setInstallationState({
      templateId: null,
      state: 'idle',
      error: null,
      installedAgentId: null,
    });
    // Clear attempted installations on reset to prevent memory leak
    attemptedInstallations.current.clear();
  }, []);

  const clearAttempted = useCallback((templateId: string) => {
    attemptedInstallations.current.delete(templateId);
  }, []);

  const isInstalling = installationState.state === 'installing';
  const hasAttempted = useCallback((templateId: string) => {
    return attemptedInstallations.current.has(templateId);
  }, []);

  return {
    installationState,
    startInstallation,
    completeInstallation,
    failInstallation,
    resetInstallation,
    clearAttempted,
    isInstalling,
    hasAttempted,
  };
}