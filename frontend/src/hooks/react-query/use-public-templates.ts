import { useQuery } from '@tanstack/react-query';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || '';

export interface PublicTemplate {
  template_id: string;
  name: string;
  description?: string;
  avatar?: string;
  avatar_color?: string;
  tags: string[];
  mcp_requirements: Array<{
    qualified_name: string;
    display_name: string;
    enabled_tools?: string[];
    required_config: string[];
    custom_type?: 'sse' | 'http';
  }>;
  agentpress_tools: Record<string, any>;
  is_public: boolean;
  download_count: number;
  marketplace_published_at?: string;
  created_at: string;
  creator_name?: string;
  is_kortix_team?: boolean;
}

export function usePublicStarterTemplates() {
  return useQuery({
    queryKey: ['public-starter-templates'],
    queryFn: async (): Promise<PublicTemplate[]> => {
      const response = await fetch(`${API_URL}/public/templates/starter`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch starter templates');
      }

      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function usePublicTemplates(params?: {
  limit?: number;
  offset?: number;
  tags?: string;
}) {
  return useQuery({
    queryKey: ['public-templates', params],
    queryFn: async (): Promise<PublicTemplate[]> => {
      const searchParams = new URLSearchParams();
      if (params?.limit) searchParams.set('limit', params.limit.toString());
      if (params?.offset) searchParams.set('offset', params.offset.toString());
      if (params?.tags) searchParams.set('tags', params.tags);

      const response = await fetch(`${API_URL}/public/templates?${searchParams}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch public templates');
      }

      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}