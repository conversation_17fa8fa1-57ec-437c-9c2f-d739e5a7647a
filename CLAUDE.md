# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Atlas is an open source generalist AI agent platform with a React/Next.js frontend and Python/FastAPI backend. The system includes sandbox environments for safe code execution, MCP (Model Context Protocol) integrations, workflow automation, and comprehensive agent management.

## Development Commands

### Frontend (Next.js)
```bash
cd frontend
npm install          # Install dependencies
npm run dev          # Start development server (localhost:3000)
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run format       # Format with Prettier
npm run format:check # Check formatting
```

### Backend (Python/FastAPI)
```bash
cd backend
python setup.py                    # Configure environment with wizard
docker compose down && docker compose up --build  # Full restart
docker compose up redis rabbitmq   # Run only Redis and RabbitMQ
uv run api.py                      # Run API locally
uv run dramatiq --processes 4 --threads 4 run_agent_background  # Run worker
```

### Testing
```bash
cd backend
pytest              # Run Python tests
```

## Architecture

### Backend Structure
- **`agent/`**: Core agent execution engine with tools and prompt management
- **`agentpress/`**: Thread management and response processing framework
- **`services/`**: External integrations (Supabase, Redis, LLM providers, email)
- **`sandbox/`**: Isolated execution environments with Docker containers
- **`mcp_local/`**: MCP server management and credential handling
- **`workflows/`**: Workflow orchestration and scheduling
- **`webhooks/`**: External webhook handling
- **`flags/`**: Feature flag system with Redis backend

### Frontend Structure
- **`src/app/`**: Next.js 13+ app router pages and API routes
- **`src/components/`**: Reusable UI components organized by feature
- **`src/hooks/`**: React Query hooks for API integration
- **`src/lib/`**: Utilities, API clients, and configuration

### Key Technologies
- **Backend**: FastAPI, Supabase (PostgreSQL), Redis, RabbitMQ, Docker
- **Frontend**: Next.js 15, React 18, TanStack Query, Tailwind CSS, Radix UI
- **Authentication**: Supabase Auth with basejump multi-tenancy
- **Database**: PostgreSQL with row-level security
- **Deployment**: Docker containers with production overrides

## Environment Configuration

The project uses a setup wizard for configuration:
```bash
python setup.py  # Run from project root
```

Critical environment variables:
- **Database**: `SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY`
- **Infrastructure**: `REDIS_HOST`, `RABBITMQ_HOST` (use `localhost` for local dev)
- **LLM**: `ANTHROPIC_API_KEY`, `OPENAI_API_KEY`, `MODEL_TO_USE`
- **Background Jobs**: `QSTASH_TOKEN`, `QSTASH_URL`
- **Security**: `MCP_CREDENTIAL_ENCRYPTION_KEY`

## Agent System

### Core Components
- **Agent Run**: Main execution loop in `agent/run.py`
- **Tools**: Modular tools in `agent/tools/` (shell, files, browser, MCP, etc.)
- **Thread Management**: Session handling via `agentpress/thread_manager.py`
- **Sandbox Execution**: Isolated environments in `sandbox/`

### Tool Architecture
Tools inherit from base classes and integrate with AgentPress framework. MCP tools provide external integrations while sandbox tools enable safe code execution.

## Development Workflow

1. **Local Development**: Run Redis/RabbitMQ in Docker, API/worker locally
2. **Full Stack**: Use docker-compose for complete environment
3. **Frontend Changes**: Hot reload with `npm run dev`
4. **Backend Changes**: Restart API service or use docker rebuild

## Feature Flags

Manage features via CLI:
```bash
cd backend/flags
python setup.py enable feature_name "Description"
python setup.py disable feature_name
python setup.py list
```

Access in code:
```python
from flags.flags import is_enabled
if await is_enabled('feature_name'):
    # Feature logic
```

## Database Migrations

Supabase migrations in `backend/supabase/migrations/`. Apply via Supabase CLI or dashboard.

## Production Deployment

```bash
docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

Uses resource limits and production configurations for Redis, API, and worker services.