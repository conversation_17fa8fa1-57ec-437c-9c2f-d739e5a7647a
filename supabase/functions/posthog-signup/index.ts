import { serve } from "https://deno.land/std@0.177.0/http/server.ts";

// Get PostHog API key from environment variable
const POSTHOG_API_KEY = Deno.env.get("POSTHOG_API_KEY");
const POSTHOG_API_URL = "https://app.posthog.com/capture/";

serve(async (req) => {
  if (!POSTHOG_API_KEY) {
    return new Response("Missing PostHog API key", { status: 500 });
  }

  // Parse the incoming Supabase Auth Hook payload
  let payload;
  try {
    payload = await req.json();
  } catch (e) {
    return new Response("Invalid JSON payload", { status: 400 });
  }

  // Extract user info from the payload
  const user = payload?.record;
  if (!user || !user.id || !user.email) {
    return new Response("Missing user data", { status: 400 });
  }

  // Prepare the event for PostHog
  const event = {
    api_key: POSTHOG_API_KEY,
    event: "User Signed Up",
    properties: {
      distinct_id: user.id,
      email: user.email,
      created_at: user.created_at,
      // Add more user properties if needed
    },
  };

  // Send the event to PostHog
  const resp = await fetch(POSTHOG_API_URL, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(event),
  });

  if (!resp.ok) {
    const errorText = await resp.text();
    return new Response(`Failed to send event to PostHog: ${errorText}`, { status: 500 });
  }

  return new Response("Event sent to PostHog", { status: 200 });
});